#!/usr/bin/env python3
"""
Complete flow test: JSON validation + Timeline calculation
"""
import sys
import os
import json
from datetime import datetime, timedelta

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our utilities
from assistant.date_utils import (
    validate_and_fix_json_structure, 
    parse_duration, 
    calculate_business_days,
    format_date_for_display
)


def simulate_complete_ai_plan_processing():
    """Simulate the complete AI plan processing flow with bug fixes"""
    print("🎯 Simulating complete AI plan processing flow:")
    print()
    
    # Step 1: Simulate AI response with bugs (like in logs.txt)
    ai_response_with_bugs = {
        "name": "Mobile App Testing & Quality Assurance Project Plan",
        "description": "A comprehensive 3-month testing plan for mobile application",
        "milestones": [
            {
                "name": "Test Strategy & Planning Phase",
                "description": "Foundational phase establishing testing framework",
                "estimated_duration": "2 weeks",
                "tasks": [
                    {
                        "name": "Define comprehensive test objectives",
                        "description": "Establish measurable quality gates and testing KPIs",
                        "estimated_duration": "3 days",
                        "subtasks": [
                            {
                                "name": "Conduct requirement analysis workshops",
                                "Description": "Organize collaborative sessions with product team"  # Bug: Capital D
                            },
                            {
                                "name": "Define test completion criteria",
                                "description": "Establish pass/fail thresholds and quality metrics"
                            },
                            {
                                "name": "Setup test management structure",
                                "Description": "Configure JIRA project with component labels"  # Bug: Capital D
                            }
                        ]
                    },
                    {
                        "name": "Develop device and OS coverage matrix",
                        "description": "Create statistically driven coverage plan",
                        "estimated_duration": "3 days",
                        "subtasks": [
                            {
                                "name": "Analyze analytics data",
                                "description": "Extract 3-month user device statistics"
                            },
                            {
                                "name": "Create physical vs cloud device strategy",
                                "Description": "Specify minimum 5 physical devices for testing"  # Bug: Capital D
                            }
                        ]
                    }
                ]
            },
            {
                "name": "Core Functional Validation Phase",
                "description": "Execute systematic verification of functional requirements",
                "estimated_duration": "4 weeks",
                "tasks": [
                    {
                        "name": "Execute authentication test suite",
                        "description": "Verify secure authentication workflows",
                        "estimated_duration": "5 days",
                        "subtasks": [
                            {
                                "name": "Conduct positive negative authentication tests",
                                "description": "Validate login success with valid credentials"
                            },
                            {
                                "name": "Test session management timing",
                                "Description": "Simulate token lifecycle using timestamp manipulation"  # Bug: Capital D
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("📊 Step 1: AI Response Analysis")
    print(f"   - Plan Name: {ai_response_with_bugs['name'][:50]}...")
    print(f"   - Milestones: {len(ai_response_with_bugs['milestones'])}")
    
    # Count bugs
    total_subtasks = 0
    bug_count = 0
    for milestone in ai_response_with_bugs['milestones']:
        for task in milestone['tasks']:
            if 'subtasks' in task:
                for subtask in task['subtasks']:
                    total_subtasks += 1
                    if 'Description' in subtask:
                        bug_count += 1
    
    print(f"   - Total Subtasks: {total_subtasks}")
    print(f"   - Bugs Found ('Description' typos): {bug_count}")
    print()
    
    # Step 2: Apply validation and bug fixes
    print("🔧 Step 2: JSON Validation & Bug Fixes")
    try:
        fixed_plan_data = validate_and_fix_json_structure(ai_response_with_bugs)
        print("   ✅ JSON validation successful")
        print("   ✅ Fixed 'Description' -> 'description' typos")
        print("   ✅ Validated required fields structure")
        
        # Verify fixes
        remaining_bugs = 0
        for milestone in fixed_plan_data['milestones']:
            for task in milestone['tasks']:
                if 'subtasks' in task:
                    for subtask in task['subtasks']:
                        if 'Description' in subtask:
                            remaining_bugs += 1
        
        print(f"   ✅ Bugs fixed: {bug_count - remaining_bugs}/{bug_count}")
        print()
        
    except Exception as e:
        print(f"   ❌ Validation failed: {str(e)}")
        return
    
    # Step 3: Calculate timeline
    print("📅 Step 3: Timeline Calculation")
    project_start_date = datetime(2025, 1, 15).date()  # Wednesday
    print(f"   - Project Start Date: {project_start_date}")
    
    current_date = project_start_date
    milestone_count = 0
    task_count = 0
    subtask_count = 0
    
    # Simulate timeline calculation
    for milestone in fixed_plan_data['milestones']:
        milestone_count += 1
        milestone_start = current_date
        
        print(f"   📌 Milestone {milestone_count}: {milestone['name'][:40]}...")
        
        for task in milestone['tasks']:
            task_count += 1
            task_start = current_date
            
            # Parse duration
            duration_days = parse_duration(task['estimated_duration'])
            task_end = calculate_business_days(
                datetime.combine(current_date, datetime.min.time()), 
                duration_days
            ).date()
            
            print(f"      📋 Task {task_count}: {task['name'][:30]}...")
            print(f"         Duration: {task['estimated_duration']} ({duration_days} business days)")
            print(f"         Dates: {task_start} to {task_end}")
            
            # Count subtasks
            if 'subtasks' in task:
                subtask_count += len(task['subtasks'])
            
            current_date = task_end + timedelta(days=1)
        
        milestone_end = current_date - timedelta(days=1)
        print(f"      🏁 Milestone Duration: {milestone_start} to {milestone_end}")
        print()
    
    total_duration = (current_date - project_start_date).days
    
    # Step 4: Final Summary
    print("🎉 Step 4: Final Summary")
    print(f"   ✅ Plan processed successfully!")
    print(f"   📊 Structure:")
    print(f"      - Milestones: {milestone_count}")
    print(f"      - Tasks: {task_count}")
    print(f"      - Subtasks: {subtask_count}")
    print(f"   📅 Timeline:")
    print(f"      - Start Date: {project_start_date}")
    print(f"      - End Date: {current_date - timedelta(days=1)}")
    print(f"      - Total Duration: {total_duration} calendar days")
    print(f"   🐛 Bug Fixes:")
    print(f"      - JSON typos fixed: {bug_count}")
    print(f"      - Structure validated: ✅")
    print(f"      - Dates calculated: ✅")
    print()


def test_edge_cases():
    """Test edge cases and error scenarios"""
    print("🧪 Testing Edge Cases:")
    print()
    
    # Test 1: Invalid duration strings
    print("   🔍 Test 1: Duration parsing edge cases")
    test_durations = [
        ("2 weeks", 14),
        ("invalid duration", 7),  # Should default to 7
        ("", 7),  # Empty should default to 7
        ("2-3 weeks", 21),  # Range should take first number
        ("1.5 months", 45),  # Decimal should work
    ]
    
    for duration_str, expected in test_durations:
        result = parse_duration(duration_str)
        status = "✅" if result == expected else "❌"
        print(f"      {status} '{duration_str}' -> {result} days (expected: {expected})")
    
    print()
    
    # Test 2: Business days calculation
    print("   🔍 Test 2: Business days over weekends")
    monday = datetime(2025, 1, 13)  # Monday
    friday_result = calculate_business_days(monday, 5)  # Should be next Monday
    print(f"      📅 5 business days from Monday -> {friday_result.strftime('%A, %Y-%m-%d')}")
    
    # Test starting from Friday
    friday = datetime(2025, 1, 17)  # Friday
    tuesday_result = calculate_business_days(friday, 2)  # Should be Tuesday
    print(f"      📅 2 business days from Friday -> {tuesday_result.strftime('%A, %Y-%m-%d')}")
    
    print()


def main():
    """Run complete flow test"""
    print("=" * 80)
    print("🚀 COMPLETE AI PLAN PROCESSING FLOW TEST")
    print("=" * 80)
    print()
    
    simulate_complete_ai_plan_processing()
    test_edge_cases()
    
    print("=" * 80)
    print("✅ Complete flow test finished!")
    print("🎯 Ready for production use with:")
    print("   - JSON validation and bug fixes")
    print("   - Automatic timeline calculation")
    print("   - Business days support")
    print("   - Comprehensive error handling")
    print("=" * 80)


if __name__ == "__main__":
    main()
