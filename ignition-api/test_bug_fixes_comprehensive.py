#!/usr/bin/env python3
"""
Comprehensive test for all bug fixes related to JSON parsing errors
"""
import sys
import os
import json
import re

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our utilities
from assistant.date_utils import clean_json_string, validate_and_fix_json_structure


def test_line_668_error_fix():
    """Test the specific line 668 error scenario from logs"""
    print("🎯 Testing Line 668 Error Fix:")
    print()
    
    # Simulate the exact error scenario
    problematic_json = '''
    {
        "name": "Mobile App Testing Project",
        "description": "A comprehensive testing plan",
        "milestones": [
            {
                "name": "Test Strategy Phase",
                "description": "Planning phase",
                "tasks": [
                    {
                        "name": "Define test objectives",
                        "description": "Task description",
                        "subtasks": [
                            {
                                "name": "Conduct requirement analysis",
                                "description": "Organize collaborative sessions"
                            },
                            {
                                "name": "Define completion criteria",
                                "description": "Establish pass/fail thresholds"
                            },
                            {
                                "name": "Setup test management",
                                description: "Configure JIRA project"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    '''
    
    print("   📊 Simulating exact error scenario:")
    print("      - Unquoted property name: 'description:'")
    print("      - Should cause: 'Expecting property name enclosed in double quotes'")
    
    # Test original parsing (should fail)
    try:
        json.loads(problematic_json)
        print("   ❌ Original JSON should have failed")
    except json.JSONDecodeError as e:
        print(f"   ✅ Original JSON failed as expected: {str(e)[:80]}...")
    
    # Test with our fixes
    try:
        cleaned = clean_json_string(problematic_json)
        parsed = json.loads(cleaned)
        validated = validate_and_fix_json_structure(parsed)
        
        print("   ✅ Fixed JSON parsed successfully!")
        print(f"      - Milestones: {len(validated['milestones'])}")
        print(f"      - Tasks: {len(validated['milestones'][0]['tasks'])}")
        print(f"      - Subtasks: {len(validated['milestones'][0]['tasks'][0]['subtasks'])}")
        
        # Verify the problematic field was fixed
        subtasks = validated['milestones'][0]['tasks'][0]['subtasks']
        problem_subtask = subtasks[2]  # The one with unquoted description
        if 'description' in problem_subtask:
            print(f"      - ✅ Fixed unquoted property: '{problem_subtask['description'][:30]}...'")
        else:
            print("      - ❌ Failed to fix unquoted property")
            
    except Exception as e:
        print(f"   ❌ Fix failed: {str(e)}")
    
    print()


def test_multiple_error_scenarios():
    """Test multiple error scenarios that could occur"""
    print("🧪 Testing Multiple Error Scenarios:")
    print()
    
    error_scenarios = [
        {
            "name": "Missing quotes on property",
            "json": '{ name: "Test", description: "Test desc" }',
            "expected_error": "property name"
        },
        {
            "name": "Trailing comma",
            "json": '{ "name": "Test", "description": "Test desc", }',
            "expected_error": "trailing comma"
        },
        {
            "name": "Single quotes",
            "json": "{ 'name': 'Test', 'description': 'Test desc' }",
            "expected_error": "single quotes"
        },
        {
            "name": "JavaScript comment",
            "json": '{ "name": "Test", // This is a comment\n "description": "Test desc" }',
            "expected_error": "comment"
        },
        {
            "name": "Mixed issues",
            "json": '{ name: "Test", description: \'Test desc\', // comment\n "count": 5, }',
            "expected_error": "multiple issues"
        }
    ]
    
    success_count = 0
    
    for scenario in error_scenarios:
        print(f"   🔍 {scenario['name']}:")
        
        # Test original (should fail)
        try:
            json.loads(scenario['json'])
            print("      ⚠️ Original JSON unexpectedly succeeded")
        except:
            pass  # Expected to fail
        
        # Test with fixes
        try:
            cleaned = clean_json_string(scenario['json'])
            parsed = json.loads(cleaned)
            print("      ✅ Successfully fixed and parsed")
            success_count += 1
        except Exception as e:
            print(f"      ❌ Fix failed: {str(e)[:50]}...")
    
    print(f"   📊 Success rate: {success_count}/{len(error_scenarios)} ({success_count/len(error_scenarios)*100:.1f}%)")
    print()


def test_real_world_ai_response():
    """Test with realistic AI response that might have issues"""
    print("🤖 Testing Real-World AI Response:")
    print()
    
    # Simulate a realistic AI response with potential issues
    ai_response = '''
    Here is the project plan:
    
    {
        "name": "E-commerce Platform Development",
        "description": "A comprehensive plan for building an e-commerce platform with modern technologies",
        "milestones": [
            {
                "name": "Planning and Design Phase",
                "description": "Initial planning and system design",
                "estimated_duration": "2 weeks",
                "tasks": [
                    {
                        "name": "Requirements gathering",
                        "description": "Collect and analyze business requirements",
                        "estimated_duration": "3 days",
                        "subtasks": [
                            {
                                "name": "Stakeholder interviews",
                                "description": "Conduct interviews with key stakeholders"
                            },
                            {
                                "name": "Document requirements",
                                Description: "Create detailed requirement specifications"
                            }
                        ]
                    }
                ]
            },
            {
                name: "Development Phase",
                "description": "Core development work",
                "estimated_duration": "6 weeks",
                "tasks": [
                    {
                        "name": "Backend development",
                        "description": "Develop API and database layer",
                        "estimated_duration": "3 weeks",
                        "subtasks": [
                            {
                                "name": "Database design",
                                "description": "Design database schema"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    This plan should provide a solid foundation for the project.
    '''
    
    print("   📊 AI Response Analysis:")
    print(f"      - Total length: {len(ai_response)} characters")
    print(f"      - Contains extra text: Yes")
    print(f"      - Has 'Description' typo: Yes")
    print(f"      - Has unquoted property: Yes")
    
    # Extract JSON
    json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
    if not json_match:
        print("   ❌ Could not extract JSON from response")
        return
    
    raw_json = json_match.group(0)
    
    # Test processing pipeline
    try:
        # Step 1: Clean JSON
        cleaned_json = clean_json_string(raw_json)
        print("   ✅ Step 1: JSON cleaning successful")
        
        # Step 2: Parse JSON
        parsed_data = json.loads(cleaned_json)
        print("   ✅ Step 2: JSON parsing successful")
        
        # Step 3: Validate and fix structure
        validated_data = validate_and_fix_json_structure(parsed_data)
        print("   ✅ Step 3: Structure validation successful")
        
        # Analyze results
        milestones = validated_data.get('milestones', [])
        total_tasks = sum(len(m.get('tasks', [])) for m in milestones)
        total_subtasks = sum(
            len(t.get('subtasks', []))
            for m in milestones
            for t in m.get('tasks', [])
        )
        
        print(f"   📊 Processed successfully:")
        print(f"      - Milestones: {len(milestones)}")
        print(f"      - Tasks: {total_tasks}")
        print(f"      - Subtasks: {total_subtasks}")
        
        # Check if typos were fixed
        typo_fixed = True
        for milestone in milestones:
            for task in milestone.get('tasks', []):
                for subtask in task.get('subtasks', []):
                    if 'Description' in subtask:
                        typo_fixed = False
                        break
        
        if typo_fixed:
            print("      - ✅ All 'Description' typos fixed")
        else:
            print("      - ❌ Some 'Description' typos remain")
            
    except Exception as e:
        print(f"   ❌ Processing failed: {str(e)}")
    
    print()


def main():
    """Run comprehensive bug fix tests"""
    print("=" * 80)
    print("🚀 COMPREHENSIVE BUG FIXES TESTING")
    print("=" * 80)
    print()
    
    test_line_668_error_fix()
    test_multiple_error_scenarios()
    test_real_world_ai_response()
    
    print("=" * 80)
    print("✅ Comprehensive testing completed!")
    print()
    print("🎯 Bug fixes implemented:")
    print("   ✅ JSON property name quotes")
    print("   ✅ Trailing comma removal")
    print("   ✅ JavaScript comment removal")
    print("   ✅ Single quote conversion")
    print("   ✅ Extra text extraction")
    print("   ✅ Multiple parsing strategies")
    print("   ✅ Structure validation")
    print("   ✅ Field name typo fixes")
    print()
    print("🚀 Ready to handle AI response parsing errors!")
    print("=" * 80)


if __name__ == "__main__":
    main()
