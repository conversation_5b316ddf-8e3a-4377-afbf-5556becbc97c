from django.core.management.base import BaseCommand
from django.db import transaction, models
from plans.models import Plan, PlanAccess, Invitation
from users.models import User


class Command(BaseCommand):
    help = 'Migrate existing plans to use the new access level system'

    def handle(self, *args, **options):
        self.stdout.write('Starting migration of plan access levels...')
        
        with transaction.atomic():
            # Create head owner access for all existing plans
            plans_without_access = Plan.objects.exclude(
                access_levels__user=models.F('user'),
                access_levels__is_head_owner=True
            )
            
            created_count = 0
            for plan in plans_without_access:
                if plan.user:
                    access, created = PlanAccess.objects.get_or_create(
                        user=plan.user,
                        plan=plan,
                        defaults={
                            'access_level': PlanAccess.OWNER,
                            'is_head_owner': True
                        }
                    )
                    if created:
                        created_count += 1
                        self.stdout.write(f'Created head owner access for plan: {plan.name}')
            
            # Convert accepted invitations to access levels
            accepted_invitations = Invitation.objects.filter(accepted=Invitation.ACCEPTED)
            converted_count = 0
            
            for invitation in accepted_invitations:
                try:
                    user = User.objects.get(email=invitation.email)
                    access_level = getattr(invitation, 'access_level', PlanAccess.VIEWER)
                    
                    access, created = PlanAccess.objects.get_or_create(
                        user=user,
                        plan=invitation.plan,
                        defaults={
                            'access_level': access_level,
                            'is_head_owner': False,
                            'granted_by': invitation.invited_by
                        }
                    )
                    
                    if created:
                        converted_count += 1
                        self.stdout.write(f'Converted invitation to access level for {user.email} on plan: {invitation.plan.name}')
                        
                except User.DoesNotExist:
                    self.stdout.write(f'Warning: User {invitation.email} not found for invitation to plan: {invitation.plan.name}')
                    continue
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Migration completed successfully!\n'
                f'Created {created_count} head owner access levels\n'
                f'Converted {converted_count} invitations to access levels'
            )
        )
