# Generated manually for adding start_date and end_date to Milestone model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('plans', '0024_invitation_access_level_planaccess'),
    ]

    operations = [
        migrations.AddField(
            model_name='milestone',
            name='start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='milestone',
            name='end_date',
            field=models.DateField(blank=True, null=True),
        ),
    ]
