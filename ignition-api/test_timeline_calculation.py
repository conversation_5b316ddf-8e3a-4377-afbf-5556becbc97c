#!/usr/bin/env python3
"""
Test script for timeline calculation functionality
"""
import sys
import os
from datetime import datetime, timedelta

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our date utilities
from assistant.date_utils import parse_duration, calculate_business_days, format_date_for_display


def test_parse_duration():
    """Test duration parsing functionality"""
    print("🧪 Testing parse_duration function:")
    
    test_cases = [
        ("2 weeks", 14),
        ("1 month", 30),
        ("3 days", 3),
        ("1 week", 7),
        ("2 months", 60),
        ("1.5 weeks", 10),  # 1.5 * 7 = 10.5 -> 10
        ("2-3 weeks", 14),  # Should take first number
        ("5", 5),  # Just number, assume days
        ("", 7),  # Empty string, default
        ("invalid text", 7),  # Invalid, default
    ]
    
    for duration_str, expected in test_cases:
        result = parse_duration(duration_str)
        status = "✅" if result == expected else "❌"
        print(f"   {status} '{duration_str}' -> {result} days (expected: {expected})")
    
    print()


def test_business_days():
    """Test business days calculation"""
    print("🧪 Testing calculate_business_days function:")
    
    # Test starting from Monday (2025-01-13)
    start_date = datetime(2025, 1, 13)  # Monday
    
    test_cases = [
        (1, "Tuesday"),
        (5, "Monday next week"),
        (10, "Friday next week"),
    ]
    
    for duration, description in test_cases:
        result = calculate_business_days(start_date, duration)
        print(f"   📅 {duration} business days from Monday -> {result.strftime('%A, %Y-%m-%d')} ({description})")
    
    print()


def test_date_formatting():
    """Test date formatting"""
    print("🧪 Testing format_date_for_display function:")
    
    test_cases = [
        (datetime(2025, 1, 15), "2025-01-15"),
        (datetime(2025, 1, 15).date(), "2025-01-15"),
        ("2025-01-15", "2025-01-15"),
        (None, None),
    ]
    
    for date_input, expected in test_cases:
        result = format_date_for_display(date_input)
        status = "✅" if result == expected else "❌"
        print(f"   {status} {type(date_input).__name__}: {date_input} -> {result}")
    
    print()


def simulate_plan_timeline():
    """Simulate a plan timeline calculation"""
    print("🎯 Simulating plan timeline calculation:")
    
    # Mock plan data
    plan_data = {
        "name": "Sample Project",
        "milestones": [
            {
                "name": "Planning Phase",
                "tasks": [
                    {"name": "Requirements gathering", "estimated_duration": "1 week"},
                    {"name": "System design", "estimated_duration": "2 weeks"},
                    {"name": "Architecture review", "estimated_duration": "3 days"},
                ]
            },
            {
                "name": "Development Phase", 
                "tasks": [
                    {"name": "Backend development", "estimated_duration": "3 weeks"},
                    {"name": "Frontend development", "estimated_duration": "2 weeks"},
                    {"name": "Integration testing", "estimated_duration": "1 week"},
                ]
            }
        ]
    }
    
    project_start_date = datetime(2025, 1, 15).date()  # Wednesday
    current_date = project_start_date
    
    print(f"   📅 Project Start Date: {project_start_date}")
    print()
    
    for milestone_idx, milestone in enumerate(plan_data["milestones"], 1):
        milestone_start = current_date
        print(f"   📌 Milestone {milestone_idx}: {milestone['name']}")
        
        for task_idx, task in enumerate(milestone["tasks"], 1):
            duration_days = parse_duration(task["estimated_duration"])
            end_date = calculate_business_days(
                datetime.combine(current_date, datetime.min.time()), 
                duration_days
            ).date()
            
            print(f"      📋 Task {task_idx}: {task['name']}")
            print(f"         Duration: {task['estimated_duration']} ({duration_days} business days)")
            print(f"         Dates: {current_date} to {end_date}")
            
            current_date = end_date + timedelta(days=1)
        
        milestone_end = current_date - timedelta(days=1)
        print(f"      🏁 Milestone Duration: {milestone_start} to {milestone_end}")
        print()
    
    total_duration = (current_date - project_start_date).days
    print(f"   🎉 Total Project Duration: {total_duration} calendar days")
    print(f"   📊 Project End Date: {current_date - timedelta(days=1)}")


def main():
    """Run all tests"""
    print("=" * 80)
    print("🚀 TIMELINE CALCULATION TESTING")
    print("=" * 80)
    print()
    
    test_parse_duration()
    test_business_days()
    test_date_formatting()
    simulate_plan_timeline()
    
    print("=" * 80)
    print("✅ All tests completed!")
    print("=" * 80)


if __name__ == "__main__":
    main()
