import os
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from users.models import User
from skills.models import Skill
from django.contrib.auth.hashers import make_password


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'username', 'password', 'avatar', 'address', 'phone_number', 'occupation']


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = ['name', 'id']

class ProfileSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()
    skills = SkillSerializer(many=True)

    class Meta:
        model = User
        fields = [
            'id', 'first_name', 'last_name', 'email', 'avatar', 'description',
            'address', 'phone_number', 'occupation', 'skills', 'date_joined'
        ]

    def get_avatar(self, instance):
        url_backend = os.getenv('URL_BACKEND', 'http://127.0.0.1:8000')
        try:
            if instance.avatar and instance.avatar.url:
                return url_backend + instance.avatar.url
            else:
                return url_backend + '/media/avatars/default.png?ver=1'
        except AttributeError:
            return url_backend + '/media/avatars/default.png?ver=1'


class UserConnectSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()
    skills = SkillSerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'first_name', 'last_name', 'email', 'avatar', 'description',
            'address', 'occupation', 'phone_number', 'skills'
        ]

    def get_avatar(self, instance):
        url_backend = os.getenv('URL_BACKEND', 'http://127.0.0.1:8000')
        try:
            if instance.avatar and instance.avatar.url:
                return url_backend + instance.avatar.url
            else:
                return url_backend + '/media/avatars/default.png?ver=1'
        except AttributeError:
            return url_backend + '/media/avatars/default.png?ver=1'


class UserLoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True)


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['is_superadmin'] = user.is_superuser
        return token

    def validate(self, attrs):
        data = super().validate(attrs)
        data['is_superuser'] = self.user.is_superuser
        return data


class CreateAccountSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)

    def validate_email(self, value):
        if User.objects.filter(email=value, is_active=True).exists():
            raise serializers.ValidationError(
                'Email address already exists. Try again.')
        return value

    def create(self, validated_data):
        validated_data['password'] = make_password(validated_data['password'])
        return User.objects.create(**validated_data)


class ActivateUserSerializer(serializers.Serializer):
    uid = serializers.CharField()
    token = serializers.CharField()


class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)


class PasswordResetSerializer(serializers.Serializer):
    password = serializers.CharField(write_only=True)
    password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError(
                {"password_confirm": "Password confirm not match with new password"})
        return attrs


class UpdateProfileSerializer(serializers.Serializer):
    first_name = serializers.CharField(required=True, max_length=75)
    last_name = serializers.CharField(required=True, max_length=75)
    avatar = serializers.ImageField(required=False, allow_null=True)
    description = serializers.CharField(required=False, max_length=512)
    address = serializers.CharField(required=False, max_length=255)
    occupation = serializers.CharField(required=False, max_length=75)
    phone_number = serializers.CharField(required=False, max_length=20)


class GoogleInputSerializer(serializers.Serializer):
    code = serializers.CharField(required=False)
    error = serializers.CharField(required=False)

class CheckInvitationSerializer(serializers.Serializer):
    signed_id = serializers.CharField(required=True)
    email = serializers.EmailField(required=True)
