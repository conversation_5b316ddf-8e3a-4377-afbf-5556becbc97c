# Plan planer app for students

[![Python](https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white)](https://www.python.org/)
[![Django](https://img.shields.io/badge/Django-092E20?style=for-the-badge&logo=django&logoColor=white)](https://www.djangoproject.com/)
[![OpenAI](https://img.shields.io/static/v1?style=for-the-badge&message=OpenAI&color=412991&logo=OpenAI&logoColor=FFFFFF&label=)](https://openai.com/research/overview)

## Development Environment

- **python:** 3.11
- **pip:** 23.2

## How to run this project

Run the following steps

1. Clone project and cd into directory:*

```bash
<NAME_EMAIL>:nghialuutrung/hackademic-be-api.git
cd hackademic-be-api
```

2. Create new virtualenv with the following commands:
(If you have already created a virtual environment, please proceed to step 3.)

```bash
python3.11 -m venv venv
```

3. Active virtualenv

```bash
source venv/bin/activate
```

4. Install python libs:

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

5. Database migration

```bash
python manage.py makemigrations
python manage.py migrate
```

6. Run server on local

```bash
python manage.py runserver
```
