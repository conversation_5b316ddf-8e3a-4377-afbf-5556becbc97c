import openai
from .models import Assistant, Thread, Message, Run
from .utils import convert_to_json
import os
from dotenv import load_dotenv
load_dotenv()

class OpenAIConfig:
    def __init__(self):
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    def create_thread(self):
        response = self.openai_client.beta.threads.create()

        converted_data = {item[0]: item[1] for item in response}
        assistant, created = Assistant.objects.get_or_create(
            value=os.getenv("ASSISTANT_ID"))
        new_thread = Thread(value=converted_data["id"], assistant=assistant)
        new_thread.save()

        return converted_data

    def add_message(self, threadid, message):
        thread_message = self.openai_client.beta.threads.messages.create(
            threadid,
            role="user",
            content=message,
        )
        converted_data = {item[0]: item[1] for item in thread_message}
        if 'content' in converted_data:
            content_list = converted_data['content']
            if isinstance(content_list, list) and len(content_list) > 0:
                content_data = []
                for content_item in content_list:
                    content_dict = {}
                    for pair in content_item:
                        key, value = pair
                        if key == "text":
                            # <PERSON>yển đổi 'text' từ danh sách các cặp sang từ điển
                            text_data = {sub_item[0]: sub_item[1]
                                         for sub_item in value}
                            content_dict[key] = text_data
                        else:
                            content_dict[key] = value
                    content_data.append(content_dict)
                converted_data['content'] = content_data

        message_id = converted_data["id"]
        content = converted_data["content"][0]["text"]["value"]
        thread_id = converted_data["thread_id"]
        thread, created = Thread.objects.get_or_create(value=thread_id)
        new_message = Message(
            message_id=message_id,
            value=content,
            thread=thread
        )
        new_message.save()
        return converted_data

    def create_run_assistant(self, thread_id, assistant_id):
        run = self.openai_client.beta.threads.runs.create(
            thread_id=thread_id,
            assistant_id=assistant_id
        )

        converted_data = {item[0]: item[1] for item in run}
        thread, created = Thread.objects.get_or_create(value=thread_id)
        new_run = Run(
            value=converted_data['id'],
            status=converted_data['status'],
            thread=thread,
        )
        new_run.save()
        return converted_data

    def retrieve_run_assistant(self, thread_id, run_id):
        run = self.openai_client.beta.threads.runs.retrieve(
            thread_id=thread_id,
            run_id=run_id
        )
        converted_data = {item[0]: item[1] for item in run}
        return converted_data

    def list_messages_assistant(self, thread_id):
        thread_messages = self.openai_client.beta.threads.messages.list(thread_id)
        converted_data = convert_to_json(thread_messages)

        return converted_data
