"""
Date utilities for automatic timeline calculation in AI-generated plans
"""
import re
from datetime import datetime, timedelta
from typing import Optional, <PERSON><PERSON>


def parse_duration(duration_str: str) -> int:
    """
    Parse duration string and return number of days
    
    Examples:
        "2 weeks" -> 14 days
        "1 month" -> 30 days
        "3 days" -> 3 days
        "1 week" -> 7 days
        "2 months" -> 60 days
    
    Args:
        duration_str: Duration string from AI
        
    Returns:
        int: Number of days
    """
    if not duration_str:
        return 7  # Default 1 week
    
    duration_str = duration_str.lower().strip()
    
    # Define conversion rates
    conversions = {
        'day': 1,
        'days': 1,
        'week': 7,
        'weeks': 7,
        'month': 30,
        'months': 30,
        'year': 365,
        'years': 365
    }
    
    # Try to extract number and unit
    # Patterns: "2 weeks", "1 month", "3 days", "2-3 weeks"
    patterns = [
        r'(\d+(?:\.\d+)?)\s*(day|days|week|weeks|month|months|year|years)',
        r'(\d+)-\d+\s*(day|days|week|weeks|month|months|year|years)',  # Take first number from range
        r'(\d+(?:\.\d+)?)\s*(\w+)',  # Fallback pattern
    ]
    
    for pattern in patterns:
        match = re.search(pattern, duration_str)
        if match:
            try:
                number = float(match.group(1))
                unit = match.group(2).lower()
                
                if unit in conversions:
                    return int(number * conversions[unit])
            except (ValueError, IndexError):
                continue
    
    # If no pattern matches, try to extract just numbers
    number_match = re.search(r'(\d+)', duration_str)
    if number_match:
        # Assume days if no unit specified
        return int(number_match.group(1))
    
    # Default fallback
    return 7  # 1 week default


def calculate_business_days(start_date: datetime, duration_days: int) -> datetime:
    """
    Calculate end date considering business days (skip weekends)
    
    Args:
        start_date: Start date
        duration_days: Duration in business days
        
    Returns:
        datetime: End date
    """
    current_date = start_date
    days_added = 0
    
    while days_added < duration_days:
        current_date += timedelta(days=1)
        # Skip weekends (Saturday=5, Sunday=6)
        if current_date.weekday() < 5:  # Monday=0 to Friday=4
            days_added += 1
    
    return current_date


def calculate_timeline(plan, project_start_date: Optional[datetime] = None, use_business_days: bool = True):
    """
    Calculate start_date and end_date for all tasks in a plan
    
    Args:
        plan: Plan object with milestones and tasks
        project_start_date: Project start date (default: today)
        use_business_days: Whether to use business days calculation
    """
    if project_start_date is None:
        project_start_date = datetime.now().date()
    elif isinstance(project_start_date, datetime):
        project_start_date = project_start_date.date()
    
    current_date = project_start_date
    
    # Get milestones ordered by creation (assuming they're in logical order)
    milestones = plan.milestone_set.all().order_by('id')
    
    for milestone in milestones:
        milestone_start = current_date
        
        # Get tasks ordered by creation
        tasks = milestone.task_set.all().order_by('id')
        
        for task in tasks:
            # Set task start date
            task.start_date = current_date
            
            # Parse duration and calculate end date
            duration_days = parse_duration(task.estimated_duration)
            
            if use_business_days:
                end_date = calculate_business_days(
                    datetime.combine(current_date, datetime.min.time()), 
                    duration_days
                ).date()
            else:
                end_date = current_date + timedelta(days=duration_days)
            
            task.end_date = end_date
            task.save()
            
            # Next task starts after current task ends (with 1 day buffer)
            current_date = end_date + timedelta(days=1)
        
        # Update milestone dates
        if tasks.exists():
            milestone.start_date = milestone_start
            milestone.end_date = tasks.last().end_date
            milestone.save()


def format_date_for_display(date_obj) -> str:
    """
    Format date for consistent display
    
    Args:
        date_obj: Date object
        
    Returns:
        str: Formatted date string
    """
    if date_obj is None:
        return None
    
    if isinstance(date_obj, str):
        return date_obj
    
    return date_obj.strftime('%Y-%m-%d')


def validate_project_dates(plan) -> Tuple[bool, list]:
    """
    Validate that all dates in the plan are logical

    Args:
        plan: Plan object

    Returns:
        Tuple[bool, list]: (is_valid, list_of_errors)
    """
    errors = []

    milestones = plan.milestone_set.all().order_by('id')

    for milestone in milestones:
        tasks = milestone.task_set.all().order_by('id')

        for task in tasks:
            if task.start_date and task.end_date:
                if task.start_date > task.end_date:
                    errors.append(f"Task '{task.name}': start_date is after end_date")

            if not task.start_date:
                errors.append(f"Task '{task.name}': missing start_date")

            if not task.end_date:
                errors.append(f"Task '{task.name}': missing end_date")

    return len(errors) == 0, errors


def clean_json_string(json_str: str) -> str:
    """
    Clean and fix common JSON syntax issues in AI-generated content

    Args:
        json_str: Raw JSON string from AI

    Returns:
        str: Cleaned JSON string
    """
    import re

    # Remove any text before the first {
    start_idx = json_str.find('{')
    if start_idx > 0:
        json_str = json_str[start_idx:]

    # Remove any text after the last }
    end_idx = json_str.rfind('}')
    if end_idx > 0:
        json_str = json_str[:end_idx + 1]

    # Remove JavaScript-style comments
    json_str = re.sub(r'//.*$', '', json_str, flags=re.MULTILINE)
    json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

    # Fix smart quotes and contractions FIRST (before other processing)
    # Replace smart quotes with regular quotes
    json_str = json_str.replace('"', '"').replace('"', '"')  # Left/right double quotes
    json_str = json_str.replace(''', "'").replace(''', "'")  # Left/right single quotes

    # Fix contractions with smart quotes - do this BEFORE JSON structure fixes
    contractions = [
        ('We"ll', "We'll"),   # We"ll -> We'll
        ('we"ll', "we'll"),   # we"ll -> we'll
        ('We"re', "We're"),   # We"re -> We're
        ('we"re', "we're"),   # we"re -> we're
        ('We"ve', "We've"),   # We"ve -> We've
        ('we"ve', "we've"),   # we"ve -> we've
        ('We"d', "We'd"),     # We"d -> We'd
        ('we"d', "we'd"),     # we"d -> we'd
        ('Don"t', "Don't"),   # Don"t -> Don't
        ('don"t', "don't"),   # don"t -> don't
        ('It"s', "It's"),     # It"s -> It's
        ('it"s', "it's"),     # it"s -> it's
        ('That"s', "That's"), # That"s -> That's
        ('that"s', "that's"), # that"s -> that's
    ]

    for smart_quote_form, regular_form in contractions:
        json_str = json_str.replace(smart_quote_form, regular_form)

    # Fix common JSON issues step by step with better error handling

    # Step 1: Fix unquoted property names (only when not already quoted)
    json_str = re.sub(r'(\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)

    # Step 2: Fix single quotes to double quotes (simple cases first)
    json_str = re.sub(r"'([^']*)'", r'"\1"', json_str)

    # Step 3: Remove trailing commas before } or ]
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

    # Step 4: Fix missing commas between objects/arrays
    json_str = re.sub(r'}(\s*){', r'},\1{', json_str)
    json_str = re.sub(r'](\s*)\[', r'],\1[', json_str)

    # Step 5: Fix missing commas between string and object/array/string
    json_str = re.sub(r'"(\s*){', r'",\1{', json_str)
    json_str = re.sub(r'"(\s*)\[', r'",\1[', json_str)
    json_str = re.sub(r'"(\s*)"[a-zA-Z_]', r'",\1"', json_str)  # Between strings

    # Step 6: Fix missing commas between } and "
    json_str = re.sub(r'}(\s*)"', r'},\1"', json_str)
    json_str = re.sub(r'](\s*)"', r'],\1"', json_str)

    # Step 7: Fix missing commas between number/boolean and other elements
    json_str = re.sub(r'(\d+)(\s*){', r'\1,\2{', json_str)
    json_str = re.sub(r'(\d+)(\s*)\[', r'\1,\2[', json_str)
    json_str = re.sub(r'(\d+)(\s*)"', r'\1,\2"', json_str)  # Number to string
    json_str = re.sub(r'(true|false)(\s*){', r'\1,\2{', json_str)
    json_str = re.sub(r'(true|false)(\s*)\[', r'\1,\2[', json_str)
    json_str = re.sub(r'(true|false)(\s*)"', r'\1,\2"', json_str)  # Boolean to string

    # Step 8: Fix missing commas between property-value pairs
    # Pattern: "key": "value" "nextkey": "nextvalue" -> "key": "value", "nextkey": "nextvalue"
    json_str = re.sub(r'"\s+"([a-zA-Z_][a-zA-Z0-9_]*)":', r'", "\1":', json_str)

    # Step 9: Fix problematic escape sequences and quotes in strings
    # Remove this step as it's causing invalid escape sequences
    # json_str = re.sub(r'"\s*([^"]*)"([^"]*)"([^"]*)\s*"', r'"\1\'\2\'\3"', json_str)

    # Step 10: Fix newlines in strings (replace with space for now)
    json_str = re.sub(r'"([^"]*)\n([^"]*)"', r'"\1 \2"', json_str)

    # Step 11: Clean up multiple spaces
    json_str = re.sub(r'\s+', ' ', json_str)

    # Step 12: Fix missing quotes around unquoted string values
    json_str = re.sub(r':\s*([a-zA-Z_][a-zA-Z0-9_\s]*[a-zA-Z0-9_])\s*([,}\]])', r': "\1"\2', json_str)

    # Step 13: Fix double commas
    json_str = re.sub(r',,+', ',', json_str)

    return json_str.strip()


def debug_json_error(json_str: str, error: Exception) -> str:
    """
    Debug JSON parsing errors by analyzing the error position

    Args:
        json_str: The JSON string that failed to parse
        error: The JSON parsing exception

    Returns:
        str: Debug information about the error
    """
    import json

    debug_info = []

    if hasattr(error, 'lineno') and hasattr(error, 'colno'):
        line_no = error.lineno
        col_no = error.colno

        # Split into lines to find the problematic area
        lines = json_str.split('\n')

        debug_info.append(f"Error at line {line_no}, column {col_no}")

        if line_no <= len(lines):
            problematic_line = lines[line_no - 1] if line_no > 0 else lines[0]
            debug_info.append(f"Problematic line: {problematic_line}")

            # Show context around the error
            start_col = max(0, col_no - 50)
            end_col = min(len(problematic_line), col_no + 50)
            context = problematic_line[start_col:end_col]
            debug_info.append(f"Context: ...{context}...")

            # Point to the exact character
            pointer = " " * (col_no - start_col - 1) + "^"
            debug_info.append(f"         ...{pointer}...")

    # Check for common issues around the error position
    if hasattr(error, 'pos'):
        pos = error.pos
        if pos < len(json_str):
            # Look at characters around the error position
            start = max(0, pos - 20)
            end = min(len(json_str), pos + 20)
            context = json_str[start:end]
            debug_info.append(f"Character context: ...{context}...")

            # Check for specific patterns
            char_at_error = json_str[pos] if pos < len(json_str) else 'EOF'
            debug_info.append(f"Character at error: '{char_at_error}'")

            # Look for missing commas
            if pos > 0:
                prev_chars = json_str[max(0, pos-5):pos]
                next_chars = json_str[pos:min(len(json_str), pos+5)]
                debug_info.append(f"Before: '{prev_chars}' | After: '{next_chars}'")

    return "\n".join(debug_info)


def validate_and_fix_json_structure(plan_data_dict):
    """
    Validate and fix common JSON structure issues in AI-generated plan data

    Args:
        plan_data_dict: Dictionary containing plan data from AI

    Returns:
        dict: Fixed plan data dictionary
    """
    if not isinstance(plan_data_dict, dict):
        raise ValueError("Plan data must be a dictionary")

    # Fix common typos in field names
    def fix_field_names(obj):
        if isinstance(obj, dict):
            fixed_obj = {}
            for key, value in obj.items():
                # Fix "Description" -> "description"
                if key == "Description":
                    fixed_obj["description"] = fix_field_names(value)
                else:
                    fixed_obj[key] = fix_field_names(value)
            return fixed_obj
        elif isinstance(obj, list):
            return [fix_field_names(item) for item in obj]
        else:
            return obj

    # Apply fixes
    fixed_data = fix_field_names(plan_data_dict)

    # Validate required fields
    required_fields = ['name', 'description', 'milestones']
    for field in required_fields:
        if field not in fixed_data:
            raise ValueError(f"Missing required field: {field}")

    # Validate milestones structure
    if not isinstance(fixed_data['milestones'], list):
        raise ValueError("Milestones must be a list")

    for i, milestone in enumerate(fixed_data['milestones']):
        if not isinstance(milestone, dict):
            raise ValueError(f"Milestone {i} must be a dictionary")

        milestone_required = ['name', 'description', 'tasks']
        for field in milestone_required:
            if field not in milestone:
                raise ValueError(f"Milestone {i} missing required field: {field}")

        # Validate tasks structure
        if not isinstance(milestone['tasks'], list):
            raise ValueError(f"Milestone {i} tasks must be a list")

        for j, task in enumerate(milestone['tasks']):
            if not isinstance(task, dict):
                raise ValueError(f"Milestone {i}, Task {j} must be a dictionary")

            task_required = ['name', 'description']
            for field in task_required:
                if field not in task:
                    raise ValueError(f"Milestone {i}, Task {j} missing required field: {field}")

            # Validate subtasks if present
            if 'subtasks' in task and task['subtasks']:
                if not isinstance(task['subtasks'], list):
                    raise ValueError(f"Milestone {i}, Task {j} subtasks must be a list")

                for k, subtask in enumerate(task['subtasks']):
                    if not isinstance(subtask, dict):
                        raise ValueError(f"Milestone {i}, Task {j}, Subtask {k} must be a dictionary")

                    subtask_required = ['name', 'description']
                    for field in subtask_required:
                        if field not in subtask:
                            raise ValueError(f"Milestone {i}, Task {j}, Subtask {k} missing required field: {field}")

    return fixed_data
