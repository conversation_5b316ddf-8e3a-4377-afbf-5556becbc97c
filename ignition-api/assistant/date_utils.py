"""
Date utilities for automatic timeline calculation in AI-generated plans
"""
import re
from datetime import datetime, timedelta
from typing import Optional, <PERSON><PERSON>


def parse_duration(duration_str: str) -> int:
    """
    Parse duration string and return number of days
    
    Examples:
        "2 weeks" -> 14 days
        "1 month" -> 30 days
        "3 days" -> 3 days
        "1 week" -> 7 days
        "2 months" -> 60 days
    
    Args:
        duration_str: Duration string from AI
        
    Returns:
        int: Number of days
    """
    if not duration_str:
        return 7  # Default 1 week
    
    duration_str = duration_str.lower().strip()
    
    # Define conversion rates
    conversions = {
        'day': 1,
        'days': 1,
        'week': 7,
        'weeks': 7,
        'month': 30,
        'months': 30,
        'year': 365,
        'years': 365
    }
    
    # Try to extract number and unit
    # Patterns: "2 weeks", "1 month", "3 days", "2-3 weeks"
    patterns = [
        r'(\d+(?:\.\d+)?)\s*(day|days|week|weeks|month|months|year|years)',
        r'(\d+)-\d+\s*(day|days|week|weeks|month|months|year|years)',  # Take first number from range
        r'(\d+(?:\.\d+)?)\s*(\w+)',  # Fallback pattern
    ]
    
    for pattern in patterns:
        match = re.search(pattern, duration_str)
        if match:
            try:
                number = float(match.group(1))
                unit = match.group(2).lower()
                
                if unit in conversions:
                    return int(number * conversions[unit])
            except (ValueError, IndexError):
                continue
    
    # If no pattern matches, try to extract just numbers
    number_match = re.search(r'(\d+)', duration_str)
    if number_match:
        # Assume days if no unit specified
        return int(number_match.group(1))
    
    # Default fallback
    return 7  # 1 week default


def calculate_business_days(start_date: datetime, duration_days: int) -> datetime:
    """
    Calculate end date considering business days (skip weekends)
    
    Args:
        start_date: Start date
        duration_days: Duration in business days
        
    Returns:
        datetime: End date
    """
    current_date = start_date
    days_added = 0
    
    while days_added < duration_days:
        current_date += timedelta(days=1)
        # Skip weekends (Saturday=5, Sunday=6)
        if current_date.weekday() < 5:  # Monday=0 to Friday=4
            days_added += 1
    
    return current_date


def calculate_timeline(plan, project_start_date: Optional[datetime] = None, use_business_days: bool = True):
    """
    Calculate start_date and end_date for all tasks in a plan
    
    Args:
        plan: Plan object with milestones and tasks
        project_start_date: Project start date (default: today)
        use_business_days: Whether to use business days calculation
    """
    if project_start_date is None:
        project_start_date = datetime.now().date()
    elif isinstance(project_start_date, datetime):
        project_start_date = project_start_date.date()
    
    current_date = project_start_date
    
    # Get milestones ordered by creation (assuming they're in logical order)
    milestones = plan.milestone_set.all().order_by('id')
    
    for milestone in milestones:
        milestone_start = current_date
        
        # Get tasks ordered by creation
        tasks = milestone.task_set.all().order_by('id')
        
        for task in tasks:
            # Set task start date
            task.start_date = current_date
            
            # Parse duration and calculate end date
            duration_days = parse_duration(task.estimated_duration)
            
            if use_business_days:
                end_date = calculate_business_days(
                    datetime.combine(current_date, datetime.min.time()), 
                    duration_days
                ).date()
            else:
                end_date = current_date + timedelta(days=duration_days)
            
            task.end_date = end_date
            task.save()
            
            # Next task starts after current task ends (with 1 day buffer)
            current_date = end_date + timedelta(days=1)
        
        # Update milestone dates
        if tasks.exists():
            milestone.start_date = milestone_start
            milestone.end_date = tasks.last().end_date
            milestone.save()


def format_date_for_display(date_obj) -> str:
    """
    Format date for consistent display
    
    Args:
        date_obj: Date object
        
    Returns:
        str: Formatted date string
    """
    if date_obj is None:
        return None
    
    if isinstance(date_obj, str):
        return date_obj
    
    return date_obj.strftime('%Y-%m-%d')


def validate_project_dates(plan) -> Tuple[bool, list]:
    """
    Validate that all dates in the plan are logical

    Args:
        plan: Plan object

    Returns:
        Tuple[bool, list]: (is_valid, list_of_errors)
    """
    errors = []

    milestones = plan.milestone_set.all().order_by('id')

    for milestone in milestones:
        tasks = milestone.task_set.all().order_by('id')

        for task in tasks:
            if task.start_date and task.end_date:
                if task.start_date > task.end_date:
                    errors.append(f"Task '{task.name}': start_date is after end_date")

            if not task.start_date:
                errors.append(f"Task '{task.name}': missing start_date")

            if not task.end_date:
                errors.append(f"Task '{task.name}': missing end_date")

    return len(errors) == 0, errors


def validate_and_fix_json_structure(plan_data_dict):
    """
    Validate and fix common JSON structure issues in AI-generated plan data

    Args:
        plan_data_dict: Dictionary containing plan data from AI

    Returns:
        dict: Fixed plan data dictionary
    """
    if not isinstance(plan_data_dict, dict):
        raise ValueError("Plan data must be a dictionary")

    # Fix common typos in field names
    def fix_field_names(obj):
        if isinstance(obj, dict):
            fixed_obj = {}
            for key, value in obj.items():
                # Fix "Description" -> "description"
                if key == "Description":
                    fixed_obj["description"] = fix_field_names(value)
                else:
                    fixed_obj[key] = fix_field_names(value)
            return fixed_obj
        elif isinstance(obj, list):
            return [fix_field_names(item) for item in obj]
        else:
            return obj

    # Apply fixes
    fixed_data = fix_field_names(plan_data_dict)

    # Validate required fields
    required_fields = ['name', 'description', 'milestones']
    for field in required_fields:
        if field not in fixed_data:
            raise ValueError(f"Missing required field: {field}")

    # Validate milestones structure
    if not isinstance(fixed_data['milestones'], list):
        raise ValueError("Milestones must be a list")

    for i, milestone in enumerate(fixed_data['milestones']):
        if not isinstance(milestone, dict):
            raise ValueError(f"Milestone {i} must be a dictionary")

        milestone_required = ['name', 'description', 'tasks']
        for field in milestone_required:
            if field not in milestone:
                raise ValueError(f"Milestone {i} missing required field: {field}")

        # Validate tasks structure
        if not isinstance(milestone['tasks'], list):
            raise ValueError(f"Milestone {i} tasks must be a list")

        for j, task in enumerate(milestone['tasks']):
            if not isinstance(task, dict):
                raise ValueError(f"Milestone {i}, Task {j} must be a dictionary")

            task_required = ['name', 'description']
            for field in task_required:
                if field not in task:
                    raise ValueError(f"Milestone {i}, Task {j} missing required field: {field}")

            # Validate subtasks if present
            if 'subtasks' in task and task['subtasks']:
                if not isinstance(task['subtasks'], list):
                    raise ValueError(f"Milestone {i}, Task {j} subtasks must be a list")

                for k, subtask in enumerate(task['subtasks']):
                    if not isinstance(subtask, dict):
                        raise ValueError(f"Milestone {i}, Task {j}, Subtask {k} must be a dictionary")

                    subtask_required = ['name', 'description']
                    for field in subtask_required:
                        if field not in subtask:
                            raise ValueError(f"Milestone {i}, Task {j}, Subtask {k} missing required field: {field}")

    return fixed_data
