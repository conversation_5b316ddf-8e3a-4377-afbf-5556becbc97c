"""
AI Provider abstraction layer for supporting multiple AI services.
Currently supports: OpenAI, OpenRouter
"""

import os
import json
import requests
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

load_dotenv()


class AIProvider(ABC):
    """Abstract base class for AI providers"""
    
    @abstractmethod
    def create_chat_completion(self, messages: List[Dict], model: str = None, **kwargs) -> Dict[str, Any]:
        """Create a chat completion"""
        pass
    
    @abstractmethod
    def get_default_model(self) -> str:
        """Get the default model for this provider"""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the provider is properly configured"""
        pass


class OpenAIProvider(AIProvider):
    """OpenAI provider implementation"""
    
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY')
        self.base_url = "https://api.openai.com/v1"
        self.default_model = os.getenv('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini')
    
    def create_chat_completion(self, messages: List[Dict], model: str = None, **kwargs) -> Dict[str, Any]:
        """Create chat completion using OpenAI API"""
        if not self.is_configured():
            raise ValueError("OpenAI API key not configured")
        
        try:
            from openai import OpenAI
            client = OpenAI(api_key=self.api_key)
            
            response = client.chat.completions.create(
                model=model or self.default_model,
                messages=messages,
                **kwargs
            )
            
            return {
                'content': response.choices[0].message.content,
                'model': response.model,
                'usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                },
                'provider': 'openai'
            }
        except Exception as e:
            raise Exception(f"OpenAI API error: {str(e)}")
    
    def get_default_model(self) -> str:
        return self.default_model
    
    def is_configured(self) -> bool:
        return bool(self.api_key)


class OpenRouterProvider(AIProvider):
    """OpenRouter provider implementation"""
    
    def __init__(self):
        self.api_key = os.getenv('OPENROUTER_API_KEY')
        self.base_url = "https://openrouter.ai/api/v1"
        self.default_model = os.getenv('OPENROUTER_DEFAULT_MODEL', 'anthropic/claude-3.5-sonnet')
        self.app_name = os.getenv('OPENROUTER_APP_NAME', 'Ignition')
        self.site_url = os.getenv('OPENROUTER_SITE_URL', 'https://ignition.app')
    
    def create_chat_completion(self, messages: List[Dict], model: str = None, **kwargs) -> Dict[str, Any]:
        """Create chat completion using OpenRouter API"""
        if not self.is_configured():
            raise ValueError("OpenRouter API key not configured")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": self.site_url,
            "X-Title": self.app_name,
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model or self.default_model,
            "messages": messages,
            **kwargs
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=300  # 5 minutes timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'content': data['choices'][0]['message']['content'],
                'model': data['model'],
                'usage': data.get('usage', {}),
                'provider': 'openrouter'
            }
        except requests.exceptions.RequestException as e:
            raise Exception(f"OpenRouter API error: {str(e)}")
        except (KeyError, IndexError) as e:
            raise Exception(f"OpenRouter response parsing error: {str(e)}")
    
    def get_default_model(self) -> str:
        return self.default_model
    
    def is_configured(self) -> bool:
        return bool(self.api_key)


class AIProviderManager:
    """Manager class for handling multiple AI providers"""
    
    def __init__(self):
        self.providers = {
            'openai': OpenAIProvider(),
            'openrouter': OpenRouterProvider()
        }
        self.default_provider = os.getenv('AI_PROVIDER', 'openai').lower()
    
    def get_provider(self, provider_name: str = None) -> AIProvider:
        """Get a specific provider or the default one"""
        provider_name = provider_name or self.default_provider
        
        if provider_name not in self.providers:
            raise ValueError(f"Unknown provider: {provider_name}")
        
        provider = self.providers[provider_name]
        if not provider.is_configured():
            raise ValueError(f"Provider {provider_name} is not properly configured")
        
        return provider
    
    def create_chat_completion(self, messages: List[Dict], provider: str = None, model: str = None, **kwargs) -> Dict[str, Any]:
        """Create chat completion using specified or default provider"""
        ai_provider = self.get_provider(provider)
        return ai_provider.create_chat_completion(messages, model, **kwargs)
    
    def get_available_providers(self) -> List[str]:
        """Get list of configured providers"""
        return [name for name, provider in self.providers.items() if provider.is_configured()]
    
    def get_provider_info(self) -> Dict[str, Dict]:
        """Get information about all providers"""
        info = {}
        for name, provider in self.providers.items():
            info[name] = {
                'configured': provider.is_configured(),
                'default_model': provider.get_default_model() if provider.is_configured() else None
            }
        return info


# Global instance
ai_manager = AIProviderManager()


def create_chat_completion(messages: List[Dict], provider: str = None, model: str = None, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to create chat completion.
    
    Args:
        messages: List of message dictionaries
        provider: AI provider name ('openai', 'openrouter')
        model: Model name (optional, uses provider default)
        **kwargs: Additional parameters for the API
    
    Returns:
        Dictionary with response content and metadata
    """
    return ai_manager.create_chat_completion(messages, provider, model, **kwargs)


def get_available_providers() -> List[str]:
    """Get list of available/configured AI providers"""
    return ai_manager.get_available_providers()


def get_provider_info() -> Dict[str, Dict]:
    """Get information about all AI providers"""
    return ai_manager.get_provider_info()
