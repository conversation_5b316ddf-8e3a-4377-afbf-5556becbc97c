from plans.models import Plan, Task, Milestone, Subtask, Risk
from users.models import User
from django.db import transaction


def convert_to_json(data):
    converted_data = {"object": "list", "data": [],
                      "first_id": None, "last_id": None, "has_more": False}

    for message in data:
        message_dict = {item[0]: item[1] for item in message}

        if 'content' in message_dict:
            content_list = message_dict['content']
            if isinstance(content_list, list) and len(content_list) > 0:
                content_data = []
                for content_item in content_list:
                    content_dict = {}
                    for key, value in content_item:
                        if key == "text":
                            text_data = {sub_item[0]: sub_item[1]
                                         for sub_item in value}
                            content_dict[key] = text_data
                        else:
                            content_dict[key] = value
                    content_data.append(content_dict)
                message_dict['content'] = content_data

        converted_data['data'].append(message_dict)

    if converted_data['data']:
        converted_data['first_id'] = converted_data['data'][0]['id']
        converted_data['last_id'] = converted_data['data'][-1]['id']

    return converted_data


def convert_json_text(input_text):
    """
    Extract and clean JSON from AI response text
    Handles smart quotes, contractions, and other common AI response issues
    """
    import re

    # Extract JSON portion
    json_start = input_text.find('{')
    json_end = input_text.rfind('}') + 1
    if json_start == -1 or json_end == -1:
        return None

    json_str = input_text[json_start:json_end]

    # Apply comprehensive JSON cleaning (same as date_utils.py)
    json_str = clean_ai_json_response(json_str)

    return json_str


def clean_ai_json_response(json_str):
    """
    Comprehensive JSON cleaning for AI responses
    Handles smart quotes, contractions, missing commas, and other common issues
    """
    import re

    # Remove any text before the first {
    start_idx = json_str.find('{')
    if start_idx > 0:
        json_str = json_str[start_idx:]

    # Remove any text after the last }
    end_idx = json_str.rfind('}')
    if end_idx > 0:
        json_str = json_str[:end_idx + 1]

    # Remove JavaScript-style comments
    json_str = re.sub(r'//.*$', '', json_str, flags=re.MULTILINE)
    json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

    # Fix smart quotes and contractions FIRST (before other processing)
    # Replace smart quotes with regular quotes - be more comprehensive
    json_str = json_str.replace('"', '"').replace('"', '"')  # Left/right double quotes
    json_str = json_str.replace(''', "'").replace(''', "'")  # Left/right single quotes
    json_str = json_str.replace('`', "'")  # Backticks to single quotes
    json_str = json_str.replace('´', "'")  # Acute accent to single quotes

    # Fix smart quotes that might appear as different Unicode characters
    json_str = json_str.replace('\u201c', '"').replace('\u201d', '"')  # Unicode left/right double quotes
    json_str = json_str.replace('\u2018', "'").replace('\u2019', "'")  # Unicode left/right single quotes

    # Fix contractions with smart quotes - do this BEFORE JSON structure fixes
    # Use regex to handle contractions more robustly
    contraction_patterns = [
        (r'(\w+)"ll\b', r"\1'll"),  # We"ll -> We'll
        (r'(\w+)"re\b', r"\1're"),  # We"re -> We're
        (r'(\w+)"ve\b', r"\1've"),  # We"ve -> We've
        (r'(\w+)"d\b', r"\1'd"),    # We"d -> We'd
        (r'(\w+)"t\b', r"\1't"),    # Don"t -> Don't, won"t -> won't
        (r'(\w+)"s\b', r"\1's"),    # It"s -> It's
    ]

    for pattern, replacement in contraction_patterns:
        json_str = re.sub(pattern, replacement, json_str)

    # Also do direct string replacements for common cases
    contractions = [
        ('We"ll', "We'll"),   ('we"ll', "we'll"),
        ('We"re', "We're"),   ('we"re', "we're"),
        ('We"ve', "We've"),   ('we"ve', "we've"),
        ('We"d', "We'd"),     ('we"d', "we'd"),
        ('Don"t', "Don't"),   ('don"t', "don't"),
        ('It"s', "It's"),     ('it"s', "it's"),
        ('That"s', "That's"), ('that"s', "that's"),
        ('You"ll', "You'll"), ('you"ll', "you'll"),
        ('You"re', "You're"), ('you"re', "you're"),
        ('You"ve', "You've"), ('you"ve', "you've"),
        ('Can"t', "Can't"),   ('can"t', "can't"),
        ('Won"t', "Won't"),   ('won"t', "won't"),
        ('Isn"t', "Isn't"),   ('isn"t', "isn't"),
        ('Aren"t', "Aren't"), ('aren"t', "aren't"),
        ('Doesn"t', "Doesn't"), ('doesn"t', "doesn't"),
        ('Hasn"t', "Hasn't"), ('hasn"t', "hasn't"),
        ('Haven"t', "Haven't"), ('haven"t', "haven't"),
        ('Wouldn"t', "Wouldn't"), ('wouldn"t', "wouldn't"),
        ('Shouldn"t', "Shouldn't"), ('shouldn"t', "shouldn't"),
        ('Couldn"t', "Couldn't"), ('couldn"t', "couldn't"),
    ]

    for smart_quote_form, regular_form in contractions:
        json_str = json_str.replace(smart_quote_form, regular_form)

    # Fix common JSON issues step by step

    # Step 1: Fix unquoted property names
    json_str = re.sub(r'(\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)

    # Step 2: Fix single quotes to double quotes
    json_str = re.sub(r"'([^']*)'", r'"\1"', json_str)

    # Step 3: Remove trailing commas before } or ]
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

    # Step 4: Fix missing commas between objects/arrays
    json_str = re.sub(r'}(\s*){', r'},\1{', json_str)
    json_str = re.sub(r'](\s*)\[', r'],\1[', json_str)

    # Step 5: Fix missing commas between string and object/array/string
    json_str = re.sub(r'"(\s*){', r'",\1{', json_str)
    json_str = re.sub(r'"(\s*)\[', r'",\1[', json_str)
    json_str = re.sub(r'"(\s*)"[a-zA-Z_]', r'",\1"', json_str)

    # Step 6: Fix missing commas between } and "
    json_str = re.sub(r'}(\s*)"', r'},\1"', json_str)
    json_str = re.sub(r'](\s*)"', r'],\1"', json_str)

    # Step 7: Fix missing commas between number/boolean and other elements
    json_str = re.sub(r'(\d+)(\s*){', r'\1,\2{', json_str)
    json_str = re.sub(r'(\d+)(\s*)\[', r'\1,\2[', json_str)
    json_str = re.sub(r'(\d+)(\s*)"', r'\1,\2"', json_str)
    json_str = re.sub(r'(true|false)(\s*){', r'\1,\2{', json_str)
    json_str = re.sub(r'(true|false)(\s*)\[', r'\1,\2[', json_str)
    json_str = re.sub(r'(true|false)(\s*)"', r'\1,\2"', json_str)

    # Step 8: Fix missing commas between property-value pairs
    json_str = re.sub(r'"\s+"([a-zA-Z_][a-zA-Z0-9_]*)":', r'", "\1":', json_str)

    # Step 9: Fix newlines in strings
    json_str = re.sub(r'"([^"]*)\n([^"]*)"', r'"\1 \2"', json_str)

    # Step 10: Clean up multiple spaces
    json_str = re.sub(r'\s+', ' ', json_str)

    # Step 11: Fix missing quotes around unquoted string values
    json_str = re.sub(r':\s*([a-zA-Z_][a-zA-Z0-9_\s]*[a-zA-Z0-9_])\s*([,}\]])', r': "\1"\2', json_str)

    # Step 12: Fix double commas
    json_str = re.sub(r',,+', ',', json_str)

    return json_str.strip()



def generate_openai_prompt(**kwargs):
    """
    Generate OpenAI prompt based on project details passed through kwargs.
    """
    from .prompts import get_project_planning_prompt
    return get_project_planning_prompt(**kwargs)


def save_plan_to_db(plan_data_dict, user_id):
    with transaction.atomic():
        # Save plan
        plan = Plan.objects.create(
            name=plan_data_dict['name'],
            description=plan_data_dict['description'],
            user=User.objects.get(id=user_id)
        )
        plan_data_dict['slug'] = plan.slug

        # Save milestones
        for milestone_data in plan_data_dict['milestones']:
            milestone = Milestone.objects.create(
                name=milestone_data['name'],
                description=milestone_data.get('description', ''),
                plan=plan,
                estimated_duration=milestone_data.get('estimated_duration', ''),
                success_criteria=milestone_data.get('success_criteria', '')
            )

            # Save risks if they exist
            if 'risks' in milestone_data and milestone_data['risks']:
                for risk_data in milestone_data['risks']:
                    Risk.objects.create(
                        risk=risk_data.get('risk', ''),
                        mitigation=risk_data.get('mitigation', ''),
                        milestone=milestone
                    )

            # Save tasks
            for task_data in milestone_data['tasks']:
                task = Task.objects.create(
                    name=task_data['name'],
                    description=task_data.get('description', ''),
                    milestone=milestone,
                    estimated_duration=task_data.get('estimated_duration', '')
                )

                # Save subtasks
                if 'subtasks' in task_data and task_data['subtasks']:
                    for subtask_data in task_data['subtasks']:
                        Subtask.objects.create(
                            name=subtask_data['name'],
                            description=subtask_data.get('description', ''),
                            task=task
                        )
    return plan_data_dict
