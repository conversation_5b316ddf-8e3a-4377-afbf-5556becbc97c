import os
import re
import time
import json
import logging
from openai import OpenAI
from starlette import status
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from assistant.serializers import PlanPromptSerializer
from rest_framework import status
from assistant.assistant_functions import OpenAIConfig
from assistant.utils import convert_json_text, generate_openai_prompt, save_plan_to_db
from assistant.prompts import get_assistant_system_prompt
from assistant.ai_providers import create_chat_completion, get_available_providers, get_provider_info
from assistant.date_utils import calculate_timeline, parse_duration, format_date_for_display, validate_and_fix_json_structure, clean_json_string
# from assistant.tasks import call_assistant_api
from urllib.error import HTTPError, URLError
from dotenv import load_dotenv
from django.db.models import Q
from plans.models import Plan, Milestone, Task, Subtask, Risk
from plans.serializers import PlanViewSerializer, MilestoneSerializer, TaskUpdateSerializer, SubtaskSerializer
import threading
from datetime import datetime
load_dotenv()

# Setup logging for API monitoring
logger = logging.getLogger(__name__)


class CreateAssitantThreadView(APIView):
    def post(self, request):
        openai_config = OpenAIConfig()
        try:
            thread = openai_config.create_thread()
            return Response({
                "data": thread,
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class AddMessageView(APIView):
    @swagger_auto_schema(
        operation_description="Add a message to an OpenAI thread",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['thread_id', 'message'],
            properties={
                'thread_id': openapi.Schema(type=openapi.TYPE_STRING, description='Thread ID'),
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='Message content')
            },
        ),
        responses={200: openapi.Response('Response', openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'data': openapi.Schema(type=openapi.TYPE_STRING, description='Response data'),
                'status': openapi.Schema(type=openapi.TYPE_INTEGER, description='Response status')
            }
        ))}
    )
    def post(self, request):
        openai_config = OpenAIConfig()
        try:
            thread_id = request.data.get("thread_id")
            message = request.data.get("message")
            response = openai_config.add_message(thread_id, message)
            return Response({
                "data": response,
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class CreateRunAssistantGetResultView(APIView):
    @swagger_auto_schema(
        operation_description="Add a message to an OpenAI thread",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['thread_id', 'content'],
            properties={
                'thread_id': openapi.Schema(type=openapi.TYPE_STRING, description='Thread ID'),
            },
        ),
        responses={200: openapi.Response('Response', openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'data': openapi.Schema(type=openapi.TYPE_STRING, description='Response data'),
                'status': openapi.Schema(type=openapi.TYPE_INTEGER, description='Response status')
            }
        ))}
    )
    def post(self, request):
        thread_id = request.data.get('thread_id')
        assistant_id = os.getenv("ASSISTANT_ID")

        if not thread_id or not assistant_id:
            return Response({
                "error": "Missing thread_id or assistant_id in the request."
            }, status=status.HTTP_400_BAD_REQUEST)

        openai_config = OpenAIConfig()
        try:
            response = openai_config.create_run_assistant(thread_id, assistant_id)
            run_id = response["id"]
            start_time = time.time()

            while True:
                if time.time() - start_time > 300:
                    return Response({
                        "error": "Timeout after 3 minutes",
                        "status": status.HTTP_408_REQUEST_TIMEOUT
                    }, status=status.HTTP_408_REQUEST_TIMEOUT)

                run_status = openai_config.retrieve_run_assistant(
                    thread_id, run_id)

                if run_status["status"] == 'completed':
                    result_message = openai_config.list_messages_assistant(
                        thread_id)
                    return Response({
                        "data": result_message,
                        "status": status.HTTP_200_OK
                    }, status=status.HTTP_200_OK)
                time.sleep(1)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class CreateProjectPlannerByAssistantView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=PlanPromptSerializer, responses={200: 'Plan created successfully'})
    def post(self, request):
        serializer = PlanPromptSerializer(data=request.data)
        if serializer.is_valid():
            prompt = serializer.validated_data['prompt']
            language = serializer.validated_data.get('language', 'English')
            role = serializer.validated_data.get('role', 'Project Manager')
            user = request.user
            try:
                client = OpenAI(api_key=os.environ['OPENAI_API_KEY'])
                thread = client.beta.threads.create()
                content = generate_openai_prompt(
                    prompt=prompt,
                    role=role,
                    language=language
                )

                message = client.beta.threads.messages.create(
                    thread_id=thread.id,
                    role='user',
                    content=content
                )

                run = client.beta.threads.runs.create(
                    thread_id=thread.id,
                    assistant_id=os.environ['ASSISTANT_ID'],
                )

                while True:
                    run_status = client.beta.threads.runs.retrieve(
                        thread_id=thread.id,
                        run_id=run.id
                    )

                    if run_status.status == 'completed':
                        result_message = client.beta.threads.messages.list(
                            thread_id=thread.id
                        )
                        for message in result_message.data:
                            created_plan_data = message.content[0].text.value
                            converted_data = convert_json_text(created_plan_data)
                            plan_data_dict = json.loads(converted_data, strict=False)
                            saved_plan_data = save_plan_to_db(plan_data_dict, request.user.id)
                            return Response(saved_plan_data, status=status.HTTP_200_OK)

            except HTTPError as e:
                print(f"HTTP Error occurred using Assistant API: {e}")
                return Response({
                    "message": "HTTP Error occurred using Assistant API",
                    "status": str(e)
                }, status=status.HTTP_404_NOT_FOUND)
            except URLError as e:
                print(f"URL Error occurred using Assistant API: {e}")
                return Response({
                    "message": "URL Error occurred using Assistant API",
                    "status": str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                print(f"An Error occurred: {e}")
                return Response({
                    "message": f"An error occurred: {e}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)


class CreateProjectPlannerByChatView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=PlanPromptSerializer, responses={202: 'Plan creation in progress'})
    def post(self, request):
        serializer = PlanPromptSerializer(data=request.data)
        if serializer.is_valid():
            prompt = serializer.validated_data['prompt']
            language = serializer.validated_data.get('language', 'English')
            role = serializer.validated_data.get('role', 'Project Manager')
            duration = serializer.validated_data.get('duration', '3 tháng')
            project_start_date = serializer.validated_data.get('project_start_date', datetime.now().date())
            user = request.user

            # Tạo bản ghi plan tạm thời
            plan = Plan.objects.create(
                name="Plan being generated...",
                description="Waiting for AI response...",
                user=user,
                status="pending"
            )

            # Bắt đầu thread để xử lý yêu cầu AI
            thread = threading.Thread(
                target=self.process_ai_request,
                args=(prompt, language, role, duration, user.id, plan.id, project_start_date)
            )
            thread.daemon = True  # Thread sẽ tự động kết thúc khi chương trình chính kết thúc
            thread.start()

            return Response({
                "message": "Plan creation in progress",
                "plan_id": plan.id,
                "plan_slug": plan.slug,
                "status": "pending"
            }, status=status.HTTP_202_ACCEPTED)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)

    def process_ai_request(self, prompt, language, role, duration, user_id, plan_id, project_start_date=None):
        try:
            # Lấy plan từ database
            plan = Plan.objects.get(id=plan_id)
            plan.status = "processing"
            plan.save()

            # Set default start date if not provided
            if project_start_date is None:
                project_start_date = datetime.now().date()

            logger.info(f"🤖 Starting AI plan generation - Plan ID: {plan_id}")
            logger.info(f"   - Prompt: {prompt[:100]}...")
            logger.info(f"   - Role: {role}")
            logger.info(f"   - Language: {language}")
            logger.info(f"   - Duration: {duration}")
            logger.info(f"   - Project Start Date: {project_start_date}")

            # Tạo prompt
            ai_prompt = generate_openai_prompt(
                prompt=prompt,
                role=role,
                language=language,
                duration=duration
            )

            logger.info(f"📝 Generated AI prompt length: {len(ai_prompt)} characters")

            # Gọi AI API (có thể là OpenAI hoặc OpenRouter)
            messages = [
                {"role": "system", "content": get_assistant_system_prompt()},
                {"role": "user", "content": ai_prompt}
            ]

            logger.info(f"🚀 Calling AI API...")

            # Sử dụng AI provider manager
            ai_response = create_chat_completion(messages)

            logger.info(f"✅ AI API response received - Length: {len(str(ai_response)) if ai_response else 0} characters")

            # Xử lý kết quả
            if ai_response and ai_response.get('content'):
                plan_data = ai_response['content']
                logger.info(f"📊 Processing AI response - Content length: {len(plan_data)} characters")

                json_data_match = re.search(r"\{.*\}", plan_data, re.DOTALL)

                if json_data_match:
                    plan_data_raw = json_data_match.group(0)
                    logger.info(f"🔍 Extracted raw JSON data - Length: {len(plan_data_raw)} characters")

                    # Clean JSON string before parsing
                    try:
                        plan_data_cleaned = clean_json_string(plan_data_raw)
                        logger.info(f"🧹 Cleaned JSON data - Length: {len(plan_data_cleaned)} characters")
                    except Exception as clean_error:
                        logger.warning(f"⚠️ JSON cleaning failed: {str(clean_error)}, using raw data")
                        plan_data_cleaned = plan_data_raw

                    # Try parsing with multiple strategies
                    plan_data_dict = None
                    parsing_strategies = [
                        ("json.loads(strict=False)", lambda: json.loads(plan_data_cleaned, strict=False)),
                        ("json.loads(strict=True)", lambda: json.loads(plan_data_cleaned, strict=True)),
                        ("re-cleaned + json.loads", lambda: json.loads(clean_json_string(plan_data_cleaned), strict=False)),
                        ("manual_json_fix", lambda: self._manual_json_fix_and_parse(plan_data_cleaned)),
                    ]

                    for strategy_name, parse_func in parsing_strategies:
                        try:
                            plan_data_dict = parse_func()
                            logger.info(f"✅ Successfully parsed JSON using {strategy_name}")
                            break
                        except Exception as parse_error:
                            logger.warning(f"⚠️ {strategy_name} failed: {str(parse_error)}")
                            continue

                    if plan_data_dict is None:
                        logger.error(f"❌ All JSON parsing strategies failed")
                        plan.status = "failed"
                        plan.description = f"Failed to parse AI response as valid JSON. Raw response length: {len(plan_data)} chars"
                        plan.save()

                        print("=" * 80)
                        print(f"❌ PLAN CREATION FAILED (Plan ID: {plan_id}) - JSON Parse Error")
                        print(f"Raw JSON (first 500 chars): {plan_data_raw[:500]}...")
                        print(f"Cleaned JSON (first 500 chars): {plan_data_cleaned[:500]}...")
                        print("=" * 80)
                        return

                    # Validate and fix JSON structure
                    try:
                        logger.info(f"🔍 Validating JSON structure...")
                        original_milestones = len(plan_data_dict.get('milestones', []))
                        plan_data_dict = validate_and_fix_json_structure(plan_data_dict)
                        logger.info(f"✅ JSON structure validated and fixed")
                        logger.info(f"   - Fixed field name typos (Description -> description)")
                        logger.info(f"   - Validated {original_milestones} milestones structure")
                    except ValueError as validation_error:
                        logger.error(f"❌ JSON validation failed: {str(validation_error)}")
                        plan.status = "failed"
                        plan.description = f"Invalid plan structure: {str(validation_error)}"
                        plan.save()

                        print("=" * 80)
                        print(f"❌ PLAN CREATION FAILED (Plan ID: {plan_id}) - JSON Validation Error")
                        print(f"Error: {str(validation_error)}")
                        print("=" * 80)
                        return

                    logger.info(f"   - Plan Name: {plan_data_dict.get('name', 'N/A')}")
                    logger.info(f"   - Milestones Count: {len(plan_data_dict.get('milestones', []))}")

                    # Print the complete plan data to console
                    print("=" * 80)
                    print(f"🎯 AI GENERATED PLAN DATA (Plan ID: {plan_id}) - VALIDATED")
                    print("=" * 80)
                    print(json.dumps(plan_data_dict, indent=2, ensure_ascii=False))
                    print("=" * 80)

                    # Cập nhật plan hiện có thay vì tạo mới
                    plan.name = plan_data_dict['name']
                    plan.description = plan_data_dict['description']
                    plan.status = "completed"
                    plan.save()

                    logger.info(f"💾 Updated plan basic info - Name: '{plan.name}'")

                    # Lưu milestones, tasks, subtasks
                    milestone_count = 0
                    task_count = 0
                    subtask_count = 0

                    for milestone_data in plan_data_dict['milestones']:
                        milestone = Milestone.objects.create(
                            name=milestone_data['name'],
                            description=milestone_data.get('description', ''),
                            plan=plan,
                            estimated_duration=milestone_data.get('estimated_duration', ''),
                            success_criteria=milestone_data.get('success_criteria', '')
                        )
                        milestone_count += 1
                        logger.info(f"📌 Created milestone {milestone_count}: '{milestone.name}'")

                        # Lưu risks nếu có
                        if 'risks' in milestone_data and milestone_data['risks']:
                            for risk_data in milestone_data['risks']:
                                Risk.objects.create(
                                    risk=risk_data.get('risk', ''),
                                    mitigation=risk_data.get('mitigation', ''),
                                    milestone=milestone
                                )

                        # Lưu tasks
                        for task_data in milestone_data['tasks']:
                            task = Task.objects.create(
                                name=task_data['name'],
                                description=task_data.get('description', ''),
                                milestone=milestone,
                                estimated_duration=task_data.get('estimated_duration', '')
                            )
                            task_count += 1
                            logger.info(f"   📋 Created task {task_count}: '{task.name}'")

                            # Lưu subtasks
                            if 'subtasks' in task_data and task_data['subtasks']:
                                for subtask_data in task_data['subtasks']:
                                    Subtask.objects.create(
                                        name=subtask_data['name'],
                                        description=subtask_data.get('description', ''),
                                        task=task
                                    )
                                    subtask_count += 1
                                    logger.info(f"      ✓ Created subtask {subtask_count}: '{subtask_data['name'][:50]}...'")

                    logger.info(f"🎉 Plan creation completed successfully!")
                    logger.info(f"   - Total Milestones: {milestone_count}")
                    logger.info(f"   - Total Tasks: {task_count}")
                    logger.info(f"   - Total Subtasks: {subtask_count}")

                    # Calculate timeline for all tasks
                    logger.info(f"📅 Calculating timeline starting from: {project_start_date}")
                    calculate_timeline(plan, project_start_date, use_business_days=True)
                    logger.info(f"✅ Timeline calculation completed")

                    # Print final summary with dates
                    print("=" * 80)
                    print(f"🎉 PLAN CREATION COMPLETED (Plan ID: {plan_id})")
                    print(f"   📊 Summary: {milestone_count} milestones, {task_count} tasks, {subtask_count} subtasks")
                    print(f"   📅 Project Start Date: {project_start_date}")

                    # Show first few tasks with dates as example
                    first_milestone = plan.milestone_set.first()
                    if first_milestone:
                        first_tasks = first_milestone.task_set.all()[:3]
                        print(f"   📋 Sample task dates:")
                        for task in first_tasks:
                            print(f"      - {task.name[:40]}... ({task.start_date} to {task.end_date})")
                    print("=" * 80)
                    except Exception as e:
                        logger.error(f"❌ Error processing plan data: {str(e)}")
                        plan.status = "failed"
                        plan.description = f"Error processing plan data: {str(e)}"
                        plan.save()

                        print("=" * 80)
                        print(f"❌ PLAN CREATION FAILED (Plan ID: {plan_id}) - JSON Processing Error")
                        print(f"Error: {str(e)}")
                        print("=" * 80)
                else:
                    logger.error(f"❌ No valid JSON found in AI response")
                    plan.status = "failed"
                    plan.description = "No valid JSON found in the response"
                    plan.save()

                    print("=" * 80)
                    print(f"❌ PLAN CREATION FAILED (Plan ID: {plan_id}) - No Valid JSON")
                    print("=" * 80)
            else:
                logger.error(f"❌ Invalid response structure from AI API")
                plan.status = "failed"
                plan.description = "Invalid response structure from AI API"
                plan.save()

                print("=" * 80)
                print(f"❌ PLAN CREATION FAILED (Plan ID: {plan_id}) - Invalid AI Response")
                print("=" * 80)

        except Exception as e:
            logger.error(f"❌ Critical error in AI plan generation: {str(e)}")
            # Cập nhật plan với thông báo lỗi
            try:
                plan = Plan.objects.get(id=plan_id)
                plan.status = "failed"
                plan.description = f"An error occurred: {str(e)}"
                plan.save()

                print("=" * 80)
                print(f"❌ PLAN CREATION FAILED (Plan ID: {plan_id}) - Critical Error")
                print(f"Error: {str(e)}")
                print("=" * 80)
            except:
                logger.error(f"Could not update plan status for plan_id {plan_id}: {str(e)}")
                print(f"Could not update plan status for plan_id {plan_id}: {str(e)}")

    def _manual_json_fix_and_parse(self, json_str):
        """
        Manual JSON fixing as last resort
        """
        import re

        # More aggressive fixes
        fixes = [
            # Fix property names that are clearly identifiers
            (r'(\s+)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":'),

            # Fix values that should be strings
            (r':\s*([a-zA-Z_][a-zA-Z0-9_\s]+)(\s*[,}\]])', r': "\1"\2'),

            # Remove any remaining trailing commas
            (r',(\s*[}\]])', r'\1'),

            # Fix missing commas
            (r'"\s*\n\s*"', '", "'),
            (r'}(\s*)"', '}, "'),
            (r'](\s*)"', '], "'),

            # Clean up extra whitespace
            (r'\s+', ' '),
        ]

        for pattern, replacement in fixes:
            json_str = re.sub(pattern, replacement, json_str, flags=re.MULTILINE)

        return json.loads(json_str, strict=False)


class PlanStatusView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(responses={200: 'Plan status retrieved successfully'})
    def get(self, request, plan_id):
        # Log the API call
        logger.info(f"🔍 PlanStatusView called - Plan ID: {plan_id}, User: {request.user.email if request.user else 'Anonymous'}")

        try:
            plan = Plan.objects.get(id=plan_id, user=request.user)

            # Log plan details
            logger.info(f"📋 Plan found - ID: {plan.id}, Name: '{plan.name}', Status: {plan.status}")

            if plan.status == 'completed':
                # Lấy thông tin đầy đủ của plan
                serializer = PlanViewSerializer(plan)
                response_data = serializer.data

                # Log detailed response for completed plans
                logger.info(f"✅ Returning completed plan data:")
                logger.info(f"   - Plan Name: {response_data.get('name', 'N/A')}")
                logger.info(f"   - Description: {response_data.get('description', 'N/A')[:100]}...")
                logger.info(f"   - Milestones Count: {len(response_data.get('milestone_set', []))}")
                logger.info(f"   - Created At: {response_data.get('created_at', 'N/A')}")

                # Print full response to console for debugging
                print("=" * 80)
                print(f"🎯 PLAN STATUS API RESPONSE (Plan ID: {plan_id})")
                print("=" * 80)
                print(json.dumps(response_data, indent=2, ensure_ascii=False, default=str))
                print("=" * 80)

                return Response(response_data)
            else:
                # Chỉ trả về thông tin cơ bản và trạng thái
                basic_response = {
                    'id': plan.id,
                    'slug': plan.slug,
                    'name': plan.name,
                    'description': plan.description,
                    'status': plan.status,
                    'created_at': plan.created_at
                }

                # Log basic response for non-completed plans
                logger.info(f"⏳ Returning basic plan data (Status: {plan.status}):")
                logger.info(f"   - Plan Name: {basic_response['name']}")
                logger.info(f"   - Status: {basic_response['status']}")

                # Print basic response to console
                print("=" * 80)
                print(f"⏳ PLAN STATUS API RESPONSE (Plan ID: {plan_id}) - Status: {plan.status}")
                print("=" * 80)
                print(json.dumps(basic_response, indent=2, ensure_ascii=False, default=str))
                print("=" * 80)

                return Response(basic_response)

        except Plan.DoesNotExist:
            logger.warning(f"❌ Plan not found - ID: {plan_id}, User: {request.user.email if request.user else 'Anonymous'}")
            error_response = {'error': 'Plan not found'}

            print("=" * 80)
            print(f"❌ PLAN STATUS API ERROR (Plan ID: {plan_id})")
            print("=" * 80)
            print(json.dumps(error_response, indent=2, ensure_ascii=False))
            print("=" * 80)

            return Response(error_response, status=status.HTTP_404_NOT_FOUND)


class AIProvidersInfoView(APIView):
    """API endpoint to get information about available AI providers"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(responses={200: 'AI providers information retrieved successfully'})
    def get(self, request):
        """Get information about available AI providers and their configuration status"""
        try:
            provider_info = get_provider_info()
            available_providers = get_available_providers()

            return Response({
                'available_providers': available_providers,
                'provider_details': provider_info,
                'current_default': os.getenv('AI_PROVIDER', 'openai').lower()
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Error retrieving provider information: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AIAgentActionView(APIView):
    """API endpoint for AI agent to perform actions on plans"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'action': openapi.Schema(type=openapi.TYPE_STRING, description='Action type: add_milestone, add_task, add_subtask, etc.'),
                'plan_slug': openapi.Schema(type=openapi.TYPE_STRING, description='Plan slug'),
                'data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Action data'),
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='User message for context')
            },
            required=['action', 'plan_slug', 'data']
        ),
        responses={
            200: openapi.Response(description="Action completed successfully"),
            400: openapi.Response(description="Invalid input"),
            404: openapi.Response(description="Plan not found")
        }
    )
    def post(self, request):
        try:
            action = request.data.get('action')
            plan_slug = request.data.get('plan_slug')
            data = request.data.get('data', {})
            message = request.data.get('message', '')

            # Get the plan
            plan = get_object_or_404(Plan, slug=plan_slug)

            # Check if user has permission to modify this plan
            # For now, we'll check if user is the owner or has access
            if plan.user != request.user:
                # Check if user has access through PlanAccess
                from plans.models import PlanAccess
                if not PlanAccess.objects.filter(user=request.user, plan=plan).exists():
                    return Response({
                        'error': 'You do not have permission to modify this plan'
                    }, status=status.HTTP_403_FORBIDDEN)

            result = None

            if action == 'add_milestone':
                result = self._add_milestone(plan, data)
            elif action == 'add_task':
                result = self._add_task(plan, data)
            elif action == 'add_subtask':
                result = self._add_subtask(plan, data)
            elif action == 'update_task_status':
                result = self._update_task_status(plan, data)
            else:
                return Response({
                    'error': f'Unknown action: {action}'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'message': 'Action completed successfully',
                'action': action,
                'result': result
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _add_milestone(self, plan, data):
        """Add a new milestone to the plan"""
        milestone_data = {
            'name': data.get('name', 'New Milestone'),
            'description': data.get('description', ''),
            'plan': plan.id,
            'estimated_duration': data.get('estimated_duration', ''),
            'success_criteria': data.get('success_criteria', '')
        }

        serializer = MilestoneSerializer(data=milestone_data)
        if serializer.is_valid():
            milestone = serializer.save()
            return {
                'milestone_id': milestone.id,
                'milestone_name': milestone.name,
                'milestone_data': serializer.data
            }
        else:
            raise ValueError(f'Invalid milestone data: {serializer.errors}')

    def _add_task(self, plan, data):
        """Add a new task to a milestone"""
        milestone_id = data.get('milestone_id')
        if not milestone_id:
            raise ValueError('milestone_id is required for adding tasks')

        milestone = get_object_or_404(Milestone, id=milestone_id, plan=plan)

        task_data = {
            'name': data.get('name', 'New Task'),
            'description': data.get('description', ''),
            'milestone': milestone.id
        }

        serializer = TaskUpdateSerializer(data=task_data)
        if serializer.is_valid():
            task = serializer.save()

            # Set order if not provided
            if 'order' not in task_data:
                task.order = Task.objects.filter(milestone=milestone).count()
                task.save()

            return {
                'task_id': task.id,
                'task_slug': task.slug,
                'task_name': task.name,
                'task_data': serializer.data
            }
        else:
            raise ValueError(f'Invalid task data: {serializer.errors}')

    def _add_subtask(self, plan, data):
        """Add a new subtask to a task"""
        task_slug = data.get('task_slug')
        if not task_slug:
            raise ValueError('task_slug is required for adding subtasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)

        subtask_data = {
            'name': data.get('name', 'New Subtask'),
            'description': data.get('description', ''),
            'task': task.id
        }

        serializer = SubtaskSerializer(data=subtask_data)
        if serializer.is_valid():
            subtask = serializer.save()
            return {
                'subtask_id': subtask.id,
                'subtask_slug': subtask.slug,
                'subtask_name': subtask.name,
                'subtask_data': serializer.data
            }
        else:
            raise ValueError(f'Invalid subtask data: {serializer.errors}')

    def _update_task_status(self, plan, data):
        """Update task status"""
        task_slug = data.get('task_slug')
        new_status = data.get('status')

        if not task_slug or new_status is None:
            raise ValueError('task_slug and status are required for updating task status')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)
        task.status = new_status
        task.save()

        return {
            'task_id': task.id,
            'task_slug': task.slug,
            'task_name': task.name,
            'new_status': new_status
        }
