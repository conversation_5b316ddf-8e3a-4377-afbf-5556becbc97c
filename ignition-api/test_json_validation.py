#!/usr/bin/env python3
"""
Test script for JSON validation and bug fixes
"""
import sys
import os
import json

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our validation utilities
from assistant.date_utils import validate_and_fix_json_structure


def test_json_validation_fixes():
    """Test JSON validation and common bug fixes"""
    print("🧪 Testing JSON validation and bug fixes:")
    
    # Test case 1: Fix "Description" -> "description" typo
    test_data_with_typo = {
        "name": "Test Project",
        "description": "Test description",
        "milestones": [
            {
                "name": "Test Milestone",
                "description": "Milestone description",
                "tasks": [
                    {
                        "name": "Test Task",
                        "description": "Task description",
                        "subtasks": [
                            {
                                "name": "Test Subtask",
                                "Description": "This should be fixed to lowercase"  # Bug here
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("   🔍 Test 1: Fixing 'Description' -> 'description' typo")
    try:
        fixed_data = validate_and_fix_json_structure(test_data_with_typo)
        # Check if the typo was fixed
        subtask = fixed_data['milestones'][0]['tasks'][0]['subtasks'][0]
        if 'description' in subtask and 'Description' not in subtask:
            print("   ✅ Successfully fixed 'Description' -> 'description' typo")
        else:
            print("   ❌ Failed to fix typo")
            print(f"      Subtask keys: {list(subtask.keys())}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Test case 2: Missing required fields
    test_data_missing_fields = {
        "name": "Test Project",
        # Missing description
        "milestones": [
            {
                "name": "Test Milestone",
                # Missing description and tasks
            }
        ]
    }
    
    print("   🔍 Test 2: Handling missing required fields")
    try:
        validate_and_fix_json_structure(test_data_missing_fields)
        print("   ❌ Should have failed validation")
    except ValueError as e:
        print(f"   ✅ Correctly caught validation error: {str(e)}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {str(e)}")
    
    # Test case 3: Valid data should pass
    valid_data = {
        "name": "Valid Project",
        "description": "Valid description",
        "milestones": [
            {
                "name": "Valid Milestone",
                "description": "Valid milestone description",
                "tasks": [
                    {
                        "name": "Valid Task",
                        "description": "Valid task description",
                        "subtasks": [
                            {
                                "name": "Valid Subtask",
                                "description": "Valid subtask description"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("   🔍 Test 3: Valid data should pass validation")
    try:
        fixed_data = validate_and_fix_json_structure(valid_data)
        print("   ✅ Valid data passed validation")
        print(f"      Milestones: {len(fixed_data['milestones'])}")
        print(f"      Tasks: {len(fixed_data['milestones'][0]['tasks'])}")
        print(f"      Subtasks: {len(fixed_data['milestones'][0]['tasks'][0]['subtasks'])}")
    except Exception as e:
        print(f"   ❌ Valid data failed: {str(e)}")
    
    print()


def test_real_world_scenario():
    """Test with data similar to the logs.txt file"""
    print("🎯 Testing real-world scenario (similar to logs.txt):")
    
    # Simulate data with multiple "Description" typos
    real_world_data = {
        "name": "Mobile App Testing Project",
        "description": "A comprehensive testing plan",
        "milestones": [
            {
                "name": "Test Strategy Phase",
                "description": "Planning phase",
                "tasks": [
                    {
                        "name": "Define test objectives",
                        "description": "Task description",
                        "subtasks": [
                            {
                                "name": "Conduct requirement analysis",
                                "Description": "This has the typo bug"  # Bug 1
                            },
                            {
                                "name": "Define completion criteria",
                                "description": "This is correct"
                            },
                            {
                                "name": "Setup test management",
                                "Description": "Another typo bug"  # Bug 2
                            }
                        ]
                    }
                ]
            },
            {
                "name": "Execution Phase",
                "description": "Testing execution",
                "tasks": [
                    {
                        "name": "Execute tests",
                        "description": "Execute all tests",
                        "subtasks": [
                            {
                                "name": "Monitor system",
                                "Description": "Yet another typo"  # Bug 3
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("   📊 Original data statistics:")
    print(f"      - Milestones: {len(real_world_data['milestones'])}")
    
    total_subtasks = 0
    typo_count = 0
    
    for milestone in real_world_data['milestones']:
        for task in milestone['tasks']:
            if 'subtasks' in task:
                for subtask in task['subtasks']:
                    total_subtasks += 1
                    if 'Description' in subtask:
                        typo_count += 1
    
    print(f"      - Total subtasks: {total_subtasks}")
    print(f"      - Subtasks with 'Description' typo: {typo_count}")
    
    try:
        fixed_data = validate_and_fix_json_structure(real_world_data)
        
        # Count fixed typos
        fixed_typo_count = 0
        for milestone in fixed_data['milestones']:
            for task in milestone['tasks']:
                if 'subtasks' in task:
                    for subtask in task['subtasks']:
                        if 'Description' in subtask:
                            fixed_typo_count += 1
        
        print("   ✅ Validation and fixes completed:")
        print(f"      - Remaining 'Description' typos: {fixed_typo_count}")
        print(f"      - Successfully fixed: {typo_count - fixed_typo_count} typos")
        
        # Verify all subtasks now have 'description' field
        all_have_description = True
        for milestone in fixed_data['milestones']:
            for task in milestone['tasks']:
                if 'subtasks' in task:
                    for subtask in task['subtasks']:
                        if 'description' not in subtask:
                            all_have_description = False
                            break
        
        if all_have_description:
            print("      - ✅ All subtasks now have 'description' field")
        else:
            print("      - ❌ Some subtasks still missing 'description' field")
            
    except Exception as e:
        print(f"   ❌ Error processing real-world data: {str(e)}")
    
    print()


def main():
    """Run all tests"""
    print("=" * 80)
    print("🚀 JSON VALIDATION AND BUG FIXES TESTING")
    print("=" * 80)
    print()
    
    test_json_validation_fixes()
    test_real_world_scenario()
    
    print("=" * 80)
    print("✅ All JSON validation tests completed!")
    print("=" * 80)


if __name__ == "__main__":
    main()
