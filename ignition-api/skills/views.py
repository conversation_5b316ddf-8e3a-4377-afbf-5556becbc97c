from django.shortcuts import render
from skills.models import Skill
from skills.serializers import SkillSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from starlette import status
from drf_yasg.utils import swagger_auto_schema
from django.shortcuts import get_object_or_404
from plans.models import Task


class UserSkillsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        skills = Skill.objects.filter(user=user)
        serializer = SkillSerializer(skills, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AddSkillForUserView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=SkillSerializer, responses={201: 'Skill created successfully'})
    def post(self, request):
        serializer = SkillSerializer(data=request.data)
        if serializer.is_valid():
            name = serializer.validated_data['name']
            description = serializer.validated_data.get('description', '')
            skill = Skill.objects.create(
                name=name,
                description=description,
                user=request.user
            )
            return Response(SkillSerializer(skill).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class deleteSkillsView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, id):
        skill = get_object_or_404(Skill, pk=id)
        self.check_object_permissions(request, skill)
        skill.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
