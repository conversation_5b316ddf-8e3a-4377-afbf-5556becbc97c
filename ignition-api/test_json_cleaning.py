#!/usr/bin/env python3
"""
Test script for JSON cleaning functionality to fix parsing errors
"""
import sys
import os
import json

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our utilities
from assistant.date_utils import clean_json_string, validate_and_fix_json_structure


def test_json_cleaning():
    """Test JSON cleaning for common AI response issues"""
    print("🧪 Testing JSON cleaning functionality:")
    print()
    
    # Test case 1: Unquoted property names (common AI mistake)
    test_case_1 = '''
    {
        name: "Test Project",
        description: "Test description",
        milestones: [
            {
                name: "Test Milestone",
                tasks: []
            }
        ]
    }
    '''
    
    print("   🔍 Test 1: Unquoted property names")
    try:
        cleaned = clean_json_string(test_case_1)
        parsed = json.loads(cleaned)
        print("   ✅ Successfully cleaned and parsed unquoted property names")
        print(f"      Original length: {len(test_case_1)}")
        print(f"      Cleaned length: {len(cleaned)}")
    except Exception as e:
        print(f"   ❌ Failed: {str(e)}")
    
    # Test case 2: Trailing commas
    test_case_2 = '''
    {
        "name": "Test Project",
        "description": "Test description",
        "milestones": [
            {
                "name": "Test Milestone",
                "tasks": [],
            },
        ],
    }
    '''
    
    print("   🔍 Test 2: Trailing commas")
    try:
        cleaned = clean_json_string(test_case_2)
        parsed = json.loads(cleaned)
        print("   ✅ Successfully cleaned and parsed trailing commas")
    except Exception as e:
        print(f"   ❌ Failed: {str(e)}")
    
    # Test case 3: Single quotes instead of double quotes
    test_case_3 = '''
    {
        'name': 'Test Project',
        'description': 'Test description with "nested quotes"',
        'milestones': []
    }
    '''
    
    print("   🔍 Test 3: Single quotes")
    try:
        cleaned = clean_json_string(test_case_3)
        parsed = json.loads(cleaned)
        print("   ✅ Successfully cleaned and parsed single quotes")
    except Exception as e:
        print(f"   ❌ Failed: {str(e)}")
    
    # Test case 4: Text before and after JSON
    test_case_4 = '''
    Here is the plan data:
    
    {
        "name": "Test Project",
        "description": "Test description",
        "milestones": []
    }
    
    That's the complete plan structure.
    '''
    
    print("   🔍 Test 4: Extra text around JSON")
    try:
        cleaned = clean_json_string(test_case_4)
        parsed = json.loads(cleaned)
        print("   ✅ Successfully extracted and parsed JSON from text")
    except Exception as e:
        print(f"   ❌ Failed: {str(e)}")
    
    print()


def test_real_world_error_scenario():
    """Test with scenario similar to the error in logs"""
    print("🎯 Testing real-world error scenario:")
    print()
    
    # Simulate the type of JSON that might cause "Expecting property name enclosed in double quotes"
    problematic_json = '''
    {
        "name": "Mobile App Testing Project",
        "description": "A comprehensive testing plan",
        "milestones": [
            {
                "name": "Test Strategy Phase",
                "description": "Planning phase",
                "tasks": [
                    {
                        "name": "Define test objectives",
                        "description": "Task description",
                        "subtasks": [
                            {
                                "name": "Conduct requirement analysis",
                                "description": "Organize collaborative sessions"
                            },
                            {
                                "name": "Define completion criteria",
                                "description": "Establish pass/fail thresholds"
                            },
                            {
                                "name": "Setup test management",
                                description: "Configure JIRA project"  // Missing quotes + comment
                            }
                        ]
                    }
                ]
            }
        ]
    }
    '''
    
    print("   📊 Original problematic JSON:")
    print(f"      - Contains unquoted property name")
    print(f"      - Contains JavaScript-style comment")
    print(f"      - Length: {len(problematic_json)} characters")
    
    # Test multiple parsing strategies
    strategies = [
        ("Raw JSON", lambda: json.loads(problematic_json)),
        ("Cleaned JSON", lambda: json.loads(clean_json_string(problematic_json))),
        ("Cleaned + Validated", lambda: validate_and_fix_json_structure(json.loads(clean_json_string(problematic_json)))),
    ]
    
    for strategy_name, parse_func in strategies:
        try:
            result = parse_func()
            print(f"   ✅ {strategy_name}: Success")
            if isinstance(result, dict) and 'milestones' in result:
                milestone_count = len(result['milestones'])
                task_count = sum(len(m.get('tasks', [])) for m in result['milestones'])
                print(f"      - Milestones: {milestone_count}, Tasks: {task_count}")
        except Exception as e:
            print(f"   ❌ {strategy_name}: {str(e)[:100]}...")
    
    print()


def test_edge_cases():
    """Test edge cases that might break JSON parsing"""
    print("🧪 Testing edge cases:")
    print()
    
    edge_cases = [
        # Case 1: Newlines in strings
        ('Newlines in strings', '''{"description": "Line 1\nLine 2\nLine 3"}'''),
        
        # Case 2: Multiple spaces
        ('Multiple spaces', '''{"name":    "Test"   ,   "description":     "Test desc"    }'''),
        
        # Case 3: Missing commas between objects
        ('Missing commas', '''{"a": 1} {"b": 2}'''),
        
        # Case 4: Escaped quotes
        ('Escaped quotes', '''{"description": "He said \\"Hello\\" to me"}'''),
        
        # Case 5: Empty values
        ('Empty values', '''{"name": "", "description": null, "count": 0}'''),
    ]
    
    for case_name, test_json in edge_cases:
        print(f"   🔍 {case_name}")
        try:
            # Try raw parsing first
            try:
                json.loads(test_json)
                print(f"      ✅ Raw JSON already valid")
            except:
                # Try with cleaning
                cleaned = clean_json_string(test_json)
                json.loads(cleaned)
                print(f"      ✅ Fixed with cleaning")
        except Exception as e:
            print(f"      ❌ Still failed: {str(e)[:50]}...")
    
    print()


def simulate_line_668_error():
    """Simulate the specific error from logs: line 668 column 13"""
    print("🎯 Simulating line 668 error scenario:")
    print()
    
    # Create a JSON with many lines to simulate line 668 error
    large_json_parts = [
        '{\n  "name": "Large Project",\n  "description": "A very large project",\n  "milestones": ['
    ]
    
    # Add many milestones to reach line 668
    for i in range(20):
        milestone = f'''
    {{
      "name": "Milestone {i+1}",
      "description": "Milestone description {i+1}",
      "tasks": [
        {{
          "name": "Task {i+1}.1",
          "description": "Task description",
          "subtasks": [
            {{
              "name": "Subtask {i+1}.1.1",
              "description": "Subtask description"
            }},
            {{
              "name": "Subtask {i+1}.1.2",
              "description": "Subtask description"
            }}
          ]
        }}
      ]
    }}'''
        
        if i < 19:  # Add comma except for last item
            milestone += ','
        
        large_json_parts.append(milestone)
    
    # Add problematic content around line 668 equivalent
    large_json_parts.append('''
  ],
  "metadata": {
    "version": "1.0",
    "created": "2025-01-15",
    invalid_property: "This will cause the error"
  }
}''')
    
    large_json = '\n'.join(large_json_parts)
    
    print(f"   📊 Generated large JSON:")
    print(f"      - Total lines: {large_json.count(chr(10)) + 1}")
    print(f"      - Total characters: {len(large_json)}")
    print(f"      - Contains unquoted property at end")
    
    # Test parsing strategies
    try:
        json.loads(large_json)
        print("   ❌ Raw JSON should have failed but didn't")
    except json.JSONDecodeError as e:
        print(f"   ✅ Raw JSON failed as expected: {str(e)}")
        print(f"      - Error at line {e.lineno}, column {e.colno}")
    
    try:
        cleaned = clean_json_string(large_json)
        result = json.loads(cleaned)
        print("   ✅ Cleaned JSON parsed successfully")
        print(f"      - Milestones: {len(result.get('milestones', []))}")
        print(f"      - Has metadata: {'metadata' in result}")
    except Exception as e:
        print(f"   ❌ Cleaned JSON still failed: {str(e)}")
    
    print()


def main():
    """Run all JSON cleaning tests"""
    print("=" * 80)
    print("🚀 JSON CLEANING AND ERROR RECOVERY TESTING")
    print("=" * 80)
    print()
    
    test_json_cleaning()
    test_real_world_error_scenario()
    test_edge_cases()
    simulate_line_668_error()
    
    print("=" * 80)
    print("✅ JSON cleaning tests completed!")
    print("🎯 The enhanced JSON processing should handle:")
    print("   - Unquoted property names")
    print("   - Trailing commas")
    print("   - Single quotes")
    print("   - Extra text around JSON")
    print("   - Multiple parsing strategies")
    print("   - Graceful error recovery")
    print("=" * 80)


if __name__ == "__main__":
    main()
