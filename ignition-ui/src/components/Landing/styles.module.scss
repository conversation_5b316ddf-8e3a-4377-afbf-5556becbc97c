/* --------- HEADER SECTION --------- */
.header {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #333;
  color: white;
  position: fixed;
  width: 100%;
  z-index: 1000;
  transition: top 0.3s ease;
  box-shadow: 0 4px 2px -2px gray;
}

.active {
  font-weight: bold;
}

.divHeader {
  margin-right: 20px;
  color: whitesmoke;
}

.linkHeader {
  color: white;
  text-decoration: none;
  font-size: 1.25rem;

  &:hover {
    color: #f0a500;
    text-decoration: none;
  }
}
/* --------- HEADER SECTION --------- */

/* --------- HERO SECTION --------- */
.hero {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  background: url('../../assets/landing-hero-backgroud-image.avif') no-repeat center center/cover;
  color: white;
  text-align: center;
  padding-top: 15vh;

  .heroSologan {
    font-size: 48px;
    margin: 0;
    animation: fadeInDown 1s ease;
    margin-top: 21vh;
    color: #333;
  }

  .heroSubSologan {
    font-size: 2rem;
    animation: fadeInUp 1s ease;
    color: gray;
  }
}

.heroBtn {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  margin-top: 20px;
  width: 160px;
  height: 54px;
  color: white;
  background-color: #333;
  transition: all 0.26s ease-in-out;
  
  font-size: 1.4rem;

  &:hover {
    background-color: #F0A500;
    color: #333;
    scale: 1.1;
    box-shadow: 0px 5px 5px gray;
    font-weight: bold;
  }
}
/* --------- HERO SECTION --------- */

/* --------- ABOUT SECTION --------- */
.titleSection {
  font-weight: bold !important;
  color: #333;
  font-size: 2.8rem;
  margin-bottom: 40px;
  position: relative;
  display: inline-block;
  animation: fadeInUp 1s ease;

  &:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #f0a500;
    animation: fadeInUp 1s ease;
  }
}

.aboutSection {
  background-color: #f9f9f9;
}

.aboutContent {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;
}

.aboutText {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
  text-align: left;
  line-height: 1.6;

  p {
    
    font-size: 1.125rem;
    color: #333;
  }
}

.aboutImage {
  flex: 1;

  img {
    width: 100%;
    max-width: 450px;
    border-radius: 8px;
  }
}
/* --------- ABOUT SECTION --------- */

/* --------- FEATURE, SERVICES, TESTIMONIALS SECTION --------- */
.featuresSection {
  background-color: #333;

  .titleCard {
    padding-top: 15px;
    margin-bottom: 10px;
    
    color: #333;
    font-weight: bold;
    font-size: 1.125rem;
  }

  .subTitleCard {
    
    color: #333;
  }

  .imagesFeature {
    width: 180px;
    height: 160px;
    object-fit: cover;
    border-radius: 8px;
  }
}

.featuresGrid,
.testimonialsGrid,
.servicesGrid {
  display: flex;
  justify-content: space-around;
  gap: 20px;
  flex-wrap: wrap;
}

.featuresGrid .feature {
  flex: 1;
  min-width: 250px;
  padding: 20px;
  background: #f4f4f4;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.featuresGrid .feature:hover,
.testimonialsGrid .testimonial:hover,
.servicesGrid .service:hover {
  transform: translateY(-10px) !important;
}
/* --------- FEATURE SECTION --------- */

/* --------- SERVICE SECTION --------- */
.servicesSection {
  background-color: #f9f9f9;

  .service {
    background-color: #fff;
    color: #333;
    padding: 20px;
    border-radius: 8px;
    transition: transform 0.3s ease;
  }

  .titleCard {
    margin-bottom: 10px;
    
    color: #333;
    font-weight: bold;
    font-size: 1.125rem;
  }

  .subTitleCard {
    
    color: #333;
  }

}
/* --------- SERVICE SECTION --------- */

/* --------- GALLERY SECTION --------- */
.gallerySection {
  background-color: #ffffff;

  .galleryGrid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
  }

  .galleryItem {
    overflow: hidden;
    position: relative;
    transition: transform .3s ease;

    &.large {
      flex: 2 1 200px;
      height: 400px;
    }

    &.small {
      flex: 1 1 100px;
      height: 200px;
    }

    &:hover img {
      transform: scale(1.1);
    }
  }

  .galleryImg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
    border-radius: 8px;
  }
}
/* --------- GALLERY SECTION --------- */

/* --------- TESTIMONIALS SECTION --------- */
.testimonialsSection {
  background-color: #333;
  color: white;

  .testimonial {
    background-color: #fff;
    color: #333;
    padding: 20px;
    border-radius: 8px;
    transition: transform 0.3s ease;
    flex: 1;
    min-width: 250px;

    &:hover {
      transform: translateY(-10px);
    }

    p {
      font-size: 1.125rem;
      
    }
  }
}
/* --------- TESTIMONIALS SECTION --------- */

/* --------- CONTACT SECTION --------- */
.contactSection {
  background-color: #ffffff;

  .contactUsForm {
    
    font-size: 1.125rem;
    color: #333;
    border-radius: 8px;
    max-width: 500px;
    transition: transform 0.3s ease;
    margin: 0 auto;

    &:hover {
      transform: translateY(-10px);
    }

    input,
    textarea {
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 8px;
      width: 100%;
      margin-bottom: 15px;
    }

    button {
      font-weight: bold;
      background: #333;
      color: white;
      border: 0;
      border-radius: 8px;
      cursor: pointer;
      width: 50%;
      height: 45px;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: #f0a500;
      }
    }
  }
}
/* --------- CONTACT SECTION --------- */

/* --------- KEYFRAMES SECTION --------- */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hoverHeroBtn {
  0% {
    background-color: #333;
    color: white;
    scale: 1;
  }

  100% {
    background-color: #F0A500;
    color: rgb(39, 39, 39);
    scale: 1.01;
    box-shadow: 0px 5px 5px gray;
    font-weight: bold;
  }
}
/* --------- KEYFRAMES SECTION --------- */
