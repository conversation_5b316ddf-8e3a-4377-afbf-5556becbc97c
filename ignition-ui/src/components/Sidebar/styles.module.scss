.customeNavbar {
  background-color: #1e1e2d;
  transition: all 0.3s ease;
  width: 250px;
  min-height: 100vh;
  padding: 0;
  border-right: 1px solid rgba(231, 231, 231, 0.1);

  &.collapsed {
    width: 70px;
  }
}

.navbarBrand {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
  border-bottom: 1px solid rgba(231, 231, 231, 0.1);
}

.brandLogo {
  height: 35px;
  width: auto;
  transition: all 0.3s ease;
}

.sidebarMain {
  background-color: transparent !important;
  border: none !important;
  height: calc(100vh - 140px) !important;
  padding: 0.75rem 0;

  > div {
    background-color: transparent !important;
  }
}

.sidebarMenu {
  padding: 0;
  background-color: transparent;
  &:hover {
    background-color: transparent !important;
  }

  :global(.ps-menu-button) {
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: 6px;
    transition: all 0.2s ease;
  }
}

.menuItem {
  color: #a2a3b7;
  font-size: 0.9rem;
  font-weight: 500;

  &:hover {
    color: #F0A500 !important;
    background-color: rgba(240, 165, 0, 0.1) !important;
    font-weight: 600;

    .menuIcon {
      color: #F0A500 !important;
    }
  }
}

.activeMenuItem {
  background-color: rgba(240, 165, 0, 0.1) !important;
  color: #F0A500 !important;
  font-weight: 600;

  .menuIcon {
    color: #F0A500 !important;
  }
}

.menuIcon {
  color: #a2a3b7;
  transition: all 0.2s ease;
  margin-right: 0.75rem;
  width: 22px !important;
  height: 22px !important;
}

.sidebarFooter {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 250px;
  height: 70px;
  padding: 0 1.25rem;
  background-color: rgba(30, 30, 45, 0.95);
  backdrop-filter: blur(8px);
  border-top: 1px solid rgba(231, 231, 231, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);

  &.footerCollapsed {
    width: 70px;
    justify-content: center;
  }
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 0.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(240, 165, 0, 0.05);
  }
}

.avatarWrapper {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.25s ease;
  background-color: rgba(240, 165, 0, 0.1);
  border: 2px solid transparent;
  box-shadow: 0 0 0 2px rgba(240, 165, 0, 0);

  &:hover {
    border-color: transparent;
    box-shadow: 0 0 0 2px #F0A500;
    transform: scale(1.05);
  }

  &.avatarActive {
    border-color: transparent;
    box-shadow: 0 0 0 2px #F0A500;
  }
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.userDetails {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.userName {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  opacity: 0.95;
  transition: all 0.2s ease;
  
  &:hover {
    color: #F0A500;
    opacity: 1;
    transform: translateX(2px);
  }
}

.logoutButton {
  background: none;
  border: none;
  padding: 0.625rem;
  border-radius: 10px;
  cursor: pointer;
  color: #a2a3b7;
  transition: all 0.25s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #F0A500;
    background-color: rgba(240, 165, 0, 0.1);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}