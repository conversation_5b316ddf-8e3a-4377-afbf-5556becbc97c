/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from 'react';
import axios from 'axios';
import { NavbarBrand, Navbar } from "reactstrap";
import { useDispatch, useSelector } from 'react-redux';
import { Sidebar, Menu, MenuItem } from 'react-pro-sidebar';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { setUser, clearUser } from '../../redux/userSlice';
import Iconify from 'components/Iconify/index';
import { APIURL } from "helpers/constants";
import { getHeaders, getRefreshToken, getAccessToken, setCookies, removeCookies } from "helpers/functions";
import styles from './styles.module.scss'

const SidebarComponent = (props) => {
  const { logo, onCollapseChange } = props;
  const [collapsed, setCollapsed] = React.useState(true);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const user = useSelector((state) => state.user);

  useEffect(() => {
    fetchAccountInfo();
  }, []);

  const fetchAccountInfo = async () => {
    if (!getAccessToken() && !getRefreshToken()) {
      navigate("/login", { replace: true });
      return;
    }

    try {
      const response = await axios.get(`${APIURL}/api/user/view-profile`, { headers: getHeaders() });

      if (response.status === 200) {
        dispatch(setUser({
          first_name: response.data.first_name,
          last_name: response.data.last_name,
          avatar: response.data.avatar,
          id: response.data.id,
        }));
      } else {
        handleUnauthorized(response.status, getRefreshToken());
      }
    } catch (error) {
      handleFetchError(error, getRefreshToken());
    }
  };

  const handleUnauthorized = async (status, refreshToken) => {
    if (status === 401 && refreshToken) {
      try {
        await refreshTokenFunc(refreshToken);
        await fetchAccountInfo();
      } catch {
        redirectToLogin();
      }
    } else {
      redirectToLogin();
    }
  };

  const handleFetchError = async (error, refreshToken) => {
    if (refreshToken) {
      try {
        await refreshTokenFunc(refreshToken);
        await fetchAccountInfo();
      } catch {
        redirectToLogin();
      }
    } else {
      redirectToLogin();
    }
    console.error(error);
  };

  const refreshTokenFunc = async (refreshToken) => {
    try {
      const refreshResponse = await axios.post(`${APIURL}/api/user/token-refresh`, { refresh: refreshToken });
      const newAccessToken = refreshResponse.data.access;
      setCookies('accessToken', newAccessToken)
      return newAccessToken;
    } catch (error) {
      console.error('Error refreshing token:', error);
      removeCookies('accessToken');
      removeCookies('refreshToken');
      throw error;
    }
  };

  const redirectToLogin = () => {
    removeCookies('accessToken');
    removeCookies('refreshToken');
    navigate("/login", { replace: true });
  };

  const handleLogout = () => {
    removeCookies('accessToken');
    removeCookies('refreshToken');
    dispatch(clearUser());
    navigate('/login', { replace: true });
  };

  const handleMouseEnter = () => {
    setCollapsed(false);
    onCollapseChange(false);
  };

  const handleMouseLeave = () => {
    setCollapsed(true);
    onCollapseChange(true);
  };

  return (
    <Navbar
      className={`navbar-vertical fixed-left ${collapsed ? "collapsed" : ""} ${styles.customeNavbar}`}
      expand="md"
      id="sidenav-main"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ 
        padding: 0,
        width: collapsed ? '70px' : '250px'
      }}>
      {logo && (
        <NavbarBrand className={styles.navbarBrand}>
          <Link to="/d/">
            <img
              alt={logo.imgAlt}
              className={styles.brandLogo}
              src={logo.imgSrc}
            />
          </Link>
        </NavbarBrand>
      )}

      <Sidebar
        collapsed={collapsed}
        className={styles.sidebarMain}
        width="240px"
        collapsedWidth="80px">
        <Menu className={styles.sidebarMenu}>
          <MenuItemLink
            to="/d/"
            active={location.pathname === "/d/" || (location.pathname.includes('/d/plan/') && location.pathname !== "/d/plan/create")}
            iconName="mage:dashboard-check"
            text="My Plans"
          />
          <MenuItemLink
            to="/d/plan/create"
            active={location.pathname === "/d/plan/create"}
            iconName="icons8:idea"
            text="New Idea"
          />
          <MenuItemLink
            to="/d/my/tasks/calendar"
            active={location.pathname === "/d/my/tasks/calendar"}
            iconName="ion:calendar-sharp"
            text="Tasks Schedule"
          />
          <MenuItemLink
            to="/d/connect"
            active={location.pathname === "/d/connect" || (location.pathname.includes('/d/other/'))}
            iconName="fluent:person-28-filled"
            text="Connect with Friends"
          />
          <MenuItemLink
            to="/d/notifications"
            active={location.pathname === "/d/notifications"}
            iconName="ion:notifications-sharp"
            text="Notifications"
          />
        </Menu>
      </Sidebar>

      <SidebarFooter user={user} collapsed={collapsed} handleLogout={handleLogout} />
    </Navbar>
  );
};

const MenuItemLink = ({ to, active, iconName, text }) => (
  <MenuItem
    active={active}
    component={<Link to={to} />}
    className={`${styles.menuItem} ${active ? styles.activeMenuItem : ''}`}
    icon={
      <Iconify 
        icon={iconName} 
        width={24} 
        height={24}
        className={styles.menuIcon} 
      />
    }
  >
    {text}
  </MenuItem>
);

const SidebarFooter = ({ user, collapsed, handleLogout }) => {
  const location = useLocation();
  const isProfileActive = location.pathname === '/d/profile/';

  return (
    <div className={`${styles.sidebarFooter} ${collapsed ? styles.footerCollapsed : ''}`}>
      <div className={styles.userInfo}>
        <Link to="/d/profile/" className={`${styles.avatarWrapper} ${isProfileActive ? styles.avatarActive : ''}`}>
          <img 
            alt={`${user.first_name} ${user.last_name}`} 
            src={user.avatar} 
            className={styles.avatarImage}
          />
        </Link>
        {!collapsed && (
          <div className={styles.userDetails}>
            <Link to="/d/profile/" className={styles.userName}>
              {user.first_name} {user.last_name}
            </Link>
          </div>
        )}
      </div>
      {!collapsed && (
        <button 
          className={styles.logoutButton} 
          onClick={handleLogout}
          title="Logout"
        >
          <Iconify icon="quill:off" width={24} height={24} />
        </button>
      )}
    </div>
  );
};

export default SidebarComponent;
