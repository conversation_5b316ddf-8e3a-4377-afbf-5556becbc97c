import React, { useState, useEffect } from 'react';
import axios from 'axios';
import dayjs from 'dayjs';
import { Box, Typography, IconButton, Tooltip, CircularProgress, Link } from '@mui/material';
import { APIURL, mainYellowColor } from "helpers/constants";
import { getHeaders } from "helpers/functions";
import Iconify from 'components/Iconify/index';
import styles from './rightSidebar.module.scss';
import { useNavigate } from 'react-router-dom';

const RightSidebar = ({ isOpen, onToggle }) => {
  const [allTasks, setAllTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchAllTasks();

    // Automatically update task list every 5 minutes
    const intervalId = setInterval(() => {
      fetchAllTasks();
    }, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchAllTasks = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${APIURL}/api/tasks/todo-by-status`, { headers: getHeaders() });

      // Filter out completed tasks and sort by priority and deadline
      const activeTasks = response.data.filter(task => task.status !== 3); // Not completed
      setAllTasks(activeTasks);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      setAllTasks([]);
    } finally {
      setLoading(false);
    }
  };

  const handleTaskStatusChange = async (taskId, newStatus) => {
    try {
      await axios.put(
        `${APIURL}/api/tasks/${taskId}/update-todo-status`,
        { status: newStatus },
        { headers: getHeaders() }
      );
      fetchAllTasks(); // Refresh the task list
    } catch (error) {
      console.error('Error updating task status:', error);
    }
  };

  const handlePlanClick = (planName) => {
    // Navigate to plan detail page
    navigate('/d/plans');
  };

  const getPriorityLabel = (priority) => {
    const priorityMap = {
      1: 'Low',
      2: 'Medium',
      3: 'High',
      4: 'Critical'
    };
    return priorityMap[priority] || 'Medium';
  };

  const getPriorityColor = (priority) => {
    const colorMap = {
      1: '#10b981', // green
      2: '#f59e0b', // yellow
      3: '#f97316', // orange
      4: '#ef4444'  // red
    };
    return colorMap[priority] || '#f59e0b';
  };

  // Check if a task is urgent (deadline within 3 hours or critical priority)
  const isUrgentTask = (task) => {
    if (task.priority === 4) return true; // Critical priority

    if (!task.end_date) return false;
    const endTime = dayjs(task.end_date);
    const now = dayjs();
    const hoursLeft = endTime.diff(now, 'hour');

    return hoursLeft <= 3 && hoursLeft >= 0;
  };

  // Check if a task is high priority
  const isHighPriorityTask = (task) => {
    return task.priority === 3; // High priority
  };

  // Format time remaining
  const formatTimeLeft = (task) => {
    const endTime = dayjs(task.end_date);
    const now = dayjs();
    
    if (endTime.isBefore(now)) {
      return 'Overdue';
    }
    
    const hoursLeft = endTime.diff(now, 'hour');
    const minutesLeft = endTime.diff(now, 'minute') % 60;
    
    if (hoursLeft === 0) {
      return `${minutesLeft} minutes`;
    } else if (hoursLeft < 24) {
      return `${hoursLeft} hours ${minutesLeft} minutes`;
    } else {
      const daysLeft = Math.floor(hoursLeft / 24);
      return `${daysLeft} days`;
    }
  };

  // Sort tasks by priority (high to low) then by deadline
  const sortedTasks = [...allTasks].sort((a, b) => {
    // First sort by priority (4=Critical, 3=High, 2=Medium, 1=Low)
    if (a.priority !== b.priority) {
      return (b.priority || 2) - (a.priority || 2);
    }

    // Then sort by deadline (closest first)
    if (a.end_date && b.end_date) {
      return dayjs(a.end_date).diff(dayjs(b.end_date));
    }

    // Tasks with deadlines come before tasks without
    if (a.end_date && !b.end_date) return -1;
    if (!a.end_date && b.end_date) return 1;

    return 0;
  });

  return (
    <div className={`${styles.rightSidebar} ${isOpen ? styles.open : ''}`}>
      <div className={styles.rightSidebarHeader}>
        <Typography variant="h6" className={styles.rightSidebarTitle}>
          <Iconify icon="icon-park-solid:checklist" width={22} height={22} color={mainYellowColor} />
          To-Do List
        </Typography>
        <Tooltip title="Refresh">
          <IconButton onClick={fetchAllTasks} className={styles.refreshButton}>
            <Iconify icon="material-symbols:refresh" width={18} height={18} />
          </IconButton>
        </Tooltip>
      </div>

      <div className={styles.rightSidebarContent}>
        {loading ? (
          <Box className={styles.loadingContainer}>
            <CircularProgress size={30} sx={{ color: mainYellowColor }} />
          </Box>
        ) : sortedTasks.length > 0 ? (
          sortedTasks.map((task) => {
            const isUrgent = isUrgentTask(task);
            const isHighPriority = isHighPriorityTask(task);
            const timeLeft = formatTimeLeft(task);
            
            return (
              <div 
                key={task.id || task.slug} 
                className={`${styles.taskItem} ${isUrgent ? styles.urgent : ''} ${isHighPriority && !isUrgent ? styles.priority : ''}`}
              >
                <div className={styles.taskHeader}>
                  <Typography className={styles.taskTitle}>{task.name}</Typography>
                  <Tooltip title={task.status === 3 ? 'Mark as incomplete' : 'Mark as complete'}>
                    <IconButton
                      className={styles.taskCheckbox}
                      onClick={() => handleTaskStatusChange(task.id || task.slug, task.status === 3 ? 1 : 3)}
                      size="small"
                    >
                      <Iconify
                        icon={task.status === 3 ? "material-symbols:check-circle" : "material-symbols:radio-button-unchecked"}
                        width={18}
                        height={18}
                        sx={{ color: task.status === 3 ? mainYellowColor : undefined }}
                      />
                    </IconButton>
                  </Tooltip>
                </div>

                <div className={styles.taskBadges}>
                  <span
                    className={styles.priorityBadge}
                    style={{ backgroundColor: getPriorityColor(task.priority), color: 'white' }}
                  >
                    <Iconify icon="material-symbols:priority-high" width={12} height={12} />
                    {getPriorityLabel(task.priority)}
                  </span>
                  {isUrgent && (
                    <span className={styles.urgentBadge}>
                      <Iconify icon="material-symbols:warning" width={12} height={12} />
                      Urgent
                    </span>
                  )}
                </div>
                
                {task.plan_name && (
                  <Link 
                    className={styles.planName}
                    onClick={() => handlePlanClick(task.plan_name)}
                    sx={{ cursor: 'pointer', color: mainYellowColor }}
                  >
                    {task.plan_name}
                  </Link>
                )}
                <div className={styles.taskDateCover}>
                  <Iconify icon="material-symbols:schedule" width={16} height={16} color={isUrgent ? "#f44336" : "#64748b"} />
                  <Typography className={`${styles.taskDate} ${isUrgent ? styles.urgentTime : ''}`}>
                    {task.end_date ? `Remaining: ${timeLeft}` : 'No deadline'}
                  </Typography>
                </div>
              </div>
            );
          })
        ) : (
          <div className={styles.noTasksMessage}>
            <Iconify icon="material-symbols:check-circle-outline" width={40} height={40} color={mainYellowColor} />
            <Typography>No active tasks!</Typography>
          </div>
        )}
      </div>

      <div className={styles.rightSidebarFooter}>
        <Typography variant="caption" className={styles.footerText}>
          {allTasks.length} active tasks
        </Typography>
        {isOpen && (
          <Tooltip title="Hide sidebar" placement="top">
            <IconButton onClick={onToggle} className={styles.closeButton} size="small">
              <Iconify icon="material-symbols:chevron-right" width={20} height={20} />
            </IconButton>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default RightSidebar; 