.card {
  margin-bottom: 0;
  border: none;
  border-radius: 0 !important;
  border-bottom: 1px solid #e0e0e0;

  &:last-child {
    border-bottom: none;
  }
}

.cardContentTop {
  display: flex;
  padding: 10px 0 0 !important;

  &:last-child {
    padding-bottom: 0;
  }
}

.cardContentBottom {
  padding: 0 !important;

  &:last-child {
    padding-bottom: 0;
  }
}

.optionBoxAdminList {
  background-color: #333;
  border: 1px solid #333;
  border-radius: 4px;
  font-size: 16px;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 5px;
}

.optionItem {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;

  &:hover {
    background-color: white;

    >span {
      color: #333;
      font-weight: bold;
    }
  }
}


.optionText {
  font-family: 'Recursive Variable' !important;
  font-size: 1.25rem;
  color: white;
  font-weight: bold;
  margin-left: 5px;
}

.subtaskNameLabel,
.taskNameLabel,
.milestoneNameLabel {
  font-size: 1.125rem;
  height: 40px;
  width: 90%;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  padding-left: 0;
  padding-bottom: 1px;
  font-family: 'Recursive Variable' !important;
}

.taskNameLabel {
  font-size: 1.3rem;
}

.milestoneNameLabel {
  font-size: 1.5rem;
  height: 30px;
  color: white;
  width: 90%;
}

.subtaskNameInput,
.taskNameInput,
.milestoneNameInput {
  font-family: 'Recursive Variable' !important;
  font-size: 1.125rem;
  border: 0;
  border-bottom: 1px solid #DEDEDE;
  height: 40px;
  width: 90%;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  padding-left: 0;
  background-color: transparent !important;

  &:hover {
    background-color: transparent !important;
  }
}

.taskNameInput {
  font-size: 1.3rem;
}

.milestoneNameInput {
  font-size: 1.5rem;
  height: 30px;
  color: white;
}

.boxTaskActionWrapper {
  display: flex;
  align-items: center;
  margin-top: 6px;
}

.boxTaskAction {
  display: flex;
  align-items: center;
  margin-left: 5px;
}

.boxScheduleTime {
  display: flex;
  align-items: center;
  margin-left: 10px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: bold;
  background-color: #333;
  border: 1px solid #333;
  padding: 0 3px;
  color: white;
}

.customScrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.customScrollbar::-webkit-scrollbar {
  display: none;
}

.noteDialogContainer {
  padding: 24px;
}

.noteTitle {
  font-family: 'Recursive Variable' !important;
  font-weight: bold;
  margin-bottom: 24px;
  color: #333 !important;
}

.noteInputContainer {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.noteSendButton {
  margin-left: 8px;
}

.noteCard {
  min-width: 260px;
  max-width: calc(50% - 16px);
  overflow: hidden !important;
  position: relative !important;
  border: 0;
  box-shadow: none;
}

.noteCardContent {
  background-color: #F0F0F0 !important;
  box-shadow: 0px 4px 12px #0000001a !important;
}

.noteContent {
  margin-bottom: 16px;
  font-family: 'Recursive Variable' !important;
  font-size: 1rem !important;
}

.noteFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.noteActions {
  display: flex;
  align-items: center;
}

.noteList {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  max-height: 500px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.noteList::-webkit-scrollbar {
  display: none;
}

.noNotesMessage {
  text-align: center;
  width: 100%;
  color: #888;
  margin-top: 20px;
  font-size: 1.125rem !important;
  font-family: 'Recursive Variable' !important;
}

.subtaskInputContainer {
  margin-top: 10px;
  margin-bottom: 10px;
}

.addTaskBtn {
  background-color: transparent !important;
  border: 0;
  box-shadow: none !important;
}

.milestoneHeader {
  width: 98%;
  display: flex;
  align-items: center;

  >div {
    width: calc(100% - 40px);
    margin-left: 5px;
  }
}

.addBtnBase {
  font-family: 'Recursive Variable' !important;
  font-size: 1.125rem !important;
  background-color: #333 !important;
  color: #F0A500 !important;
  font-weight: bold;

  &:hover {
    background-color: #333 !important;
    color: #F0A500;
  }

  >span {
    margin-right: 5px;
  }
}

.commentTextArea {
  font-family: 'Recursive Variable' !important;
  font-size: 1.125rem !important;
}

.titleTextarea {
  font-weight: bold;
  border-top: 1px solid #333;
  padding-top: 10px;
  font-size: 1.125rem;
  font-family: 'Recursive Variable' !important;
}

.commentEditingAction {
  display: flex;
  align-items: center;
  margin-top: 5px;
  color: #333;

  >p {
    font-family: 'Recursive Variable' !important;
    font-size: 1rem;
  }
}

.commentHeader {
  font-size: 1.8rem !important;
  font-family: 'Recursive Variable' !important;
  color: #333 !important;
}

.milestoneBody {
  .boxShowDescription {
    padding: 8px 4px 0;
    font-family: 'Recursive Variable' !important;
    display: flex;
    justify-content: flex-start;
  }

  .descriptionContent {
    font-family: 'Recursive Variable' !important;
    font-size: 0.875rem;
    color: white;
    margin-left: 10px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .expandedDescription {
    -webkit-line-clamp: unset;
    overflow: visible;
    display: block;
  }
}