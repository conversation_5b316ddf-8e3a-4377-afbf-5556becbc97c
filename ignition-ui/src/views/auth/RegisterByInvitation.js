import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import {
  InputAdornment,
  IconButton,
  Button,
  Typography,
  Box,
  Container,
  Card,
  CardContent,
  FormControl,
  CircularProgress
} from "@mui/material";
import { iconPrimaryColor, APIURL } from "helpers/constants";
import { errorSnackbar, successSnackbar } from 'components/Snackbar/index';
import Iconify from 'components/Iconify/index';
import InputBase from 'components/Input/InputBase';
import InputPasswordBase from 'components/Input/InputPasswordBase';
import Cookies from 'js-cookie';
import styles from './styles.module.scss';


const RegisterByInvitation = () => {
  const { signedId, email } = useParams();
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showPass, setShowPass] = useState(false);
  const [formData, setFormData] = useState({
    email: email,
    password: '',
    firstName: '',
    lastName: '',
  });
  const [signedIdValid, setSignedIdValid] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const navigate = useNavigate();

  const handleChange = (field, value) => {
    setFormData((prevState) => ({
      ...prevState,
      [field]: value,
    }));
  };

  const handleShowPass = () => {
    setShowPass(!showPass);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleRegister();
    }
  };

  const handleRegister = async () => {
    const { email, password, firstName, lastName } = formData;
    const registrationData = new FormData();
    registrationData.append('email', email);
    registrationData.append('password', password);
    registrationData.append('first_name', firstName);
    registrationData.append('last_name', lastName);

    setLoading(true);
    try {
      const response = await axios.post(`${APIURL}/api/user/create-through-invitation`, registrationData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      const { access_token, refresh_token } = response.data;
      Cookies.set('accessToken', access_token, { expires: 7 });
      Cookies.set('refreshToken', refresh_token, { expires: 7 });
      successSnackbar("Register Success!");
      navigate(`/p/accept-invitation/${signedId}`, { replace: true });
    } catch (error) {
      errorSnackbar(error?.response?.data?.message);
      setErrors(error.response.data || {});
    } finally {
      setLoading(false);
    }
  }

  const checkSignedId = async () => {
    setIsChecking(true);
    const formData = new FormData();
    formData.append('signed_id', signedId);
    formData.append('email', email);
    try {
      const response = await axios.post(`${APIURL}/api/user/check-signed-id`, formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
      setSignedIdValid(response.data.is_valid);
    } catch (error) {
      errorSnackbar(error?.response?.data?.message)
      setSignedIdValid(false);
    } finally {
      setIsChecking(false);
    }
  };

  const handleGoToLogin = () => {
    navigate('/login', { replace: true });
  }

  useEffect(() => {
    checkSignedId();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        {isChecking ? (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Checking Invitation
                  </Typography>
                </Box>
              </Box>
            </Box>
            <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 }, textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  py: 4
                }}
              >
                <Box sx={{ mb: 3 }}>
                  <CircularProgress size={80} sx={{ color: '#F0A500' }} />
                </Box>
                <Typography
                  variant="h5"
                  className={`${styles.fontRecursive} ${styles.successTitle}`}
                  sx={{ mb: 2, fontWeight: 600 }}
                >
                  Verifying Invitation
                </Typography>
                <Typography
                  variant="body1"
                  className={`${styles.fontRecursive} ${styles.successMessage}`}
                  sx={{ fontSize: '1.1rem' }}
                >
                  Please wait while we verify your invitation link...
                </Typography>
              </Box>
            </CardContent>
          </Card>
        ) : signedIdValid ? (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Register by Invitation
                  </Typography>
                </Box>
              </Box>
            </Box>

            <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 } }}>
              <div className={styles.divider}>
                <span>Create your account</span>
              </div>

              <Box component="form" onKeyPress={handleKeyPress} noValidate>
                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="text"
                      handleChange={handleChange}
                      errorText={errors.firstName}
                      placeholder="First Name"
                      value={formData.firstName}
                      keyword="firstName"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:person" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.firstName && <div className={styles.errorMessage}>{errors.firstName[0]}</div>}
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="text"
                      handleChange={handleChange}
                      errorText={errors.last_name}
                      placeholder="Last Name"
                      value={formData.lastName}
                      keyword="lastName"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:person" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.last_name && <div className={styles.errorMessage}>{errors.last_name[0]}</div>}
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="email"
                      handleChange={handleChange}
                      errorText={errors.email}
                      placeholder="Email"
                      value={formData.email}
                      keyword="email"
                      disabled={true}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="ic:baseline-email" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.email && <div className={styles.errorMessage}>{errors.email[0]}</div>}
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth>
                  <div className={styles.inputField}>
                    <InputPasswordBase
                      showPass={showPass}
                      handleChange={handleChange}
                      errorText={errors.password}
                      placeholder="Password"
                      value={formData.password}
                      name="password"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:lock" color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton onClick={handleShowPass} edge="end">
                              <Iconify icon={showPass ? 'mdi:eye-off' : 'mdi:eye'} color={iconPrimaryColor} />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.password && <div className={styles.errorMessage}>{errors.password[0]}</div>}
                </FormControl>

                <Box display="flex" justifyContent="center" mt={4}>
                  <Button
                    onClick={handleRegister}
                    variant="contained"
                    className={styles.submitBtn}
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Register'}
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Invalid Invitation
                  </Typography>
                </Box>
              </Box>
            </Box>

            <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 }, textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  py: 4
                }}
              >
                <Box
                  className={styles.successIconContainer}
                  sx={{
                    mb: 3,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100px',
                    height: '100px',
                    borderRadius: '50%',
                    backgroundColor: '#d32f2f',
                    border: '2px solid #d32f2f',
                    boxShadow: '0 0 15px rgba(211, 47, 47, 0.5)',
                    margin: '0 auto'
                  }}
                >
                  <Iconify
                    icon="mdi:alert"
                    width={60}
                    height={60}
                    className={styles.successIcon}
                    sx={{
                      color: '#ffffff',
                    }}
                  />
                </Box>

                <Typography
                  variant="h5"
                  className={`${styles.fontRecursive} ${styles.successTitle}`}
                  sx={{ mb: 2, fontWeight: 600 }}
                >
                  Invalid Invitation Link
                </Typography>

                <Typography
                  variant="body1"
                  className={`${styles.fontRecursive} ${styles.successMessage}`}
                  sx={{ fontSize: '1.1rem', mb: 4 }}
                >
                  This invitation link is not valid or has expired.
                </Typography>

                <Button
                  variant="contained"
                  className={styles.submitBtn}
                  onClick={handleGoToLogin}
                >
                  Return to Login
                </Button>
              </Box>
            </CardContent>
          </Card>
        )}

        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/login" className={styles.customeALink}>
            <Iconify icon="mdi:login" width={20} height={20} className={styles.linkIcon} />
            Already have an account? Sign in
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default RegisterByInvitation;
