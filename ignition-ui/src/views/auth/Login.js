import React, { useState } from "react";
import {
  InputAdornment,
  IconButton,
  Button,
  Typography,
  Box,
  Container,
  Card,
  CardContent,
  FormControl,
  CircularProgress
} from "@mui/material";
import { useNavigate } from 'react-router-dom';
import { successSnackbar, errorSnackbar } from 'components/Snackbar/index';
import { APIURL, iconPrimaryColor } from "helpers/constants";
import InputBase from 'components/Input/InputBase';
import InputPasswordBase from 'components/Input/InputPasswordBase';
import GoogleSignInButton from 'components/Button/GoogleSignInButton';
import Iconify from 'components/Iconify/index';
import Cookies from 'js-cookie';
import axios from 'axios';
import styles from './styles.module.scss';


const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showPass, setShowPass] = useState(false);
  const navigate = useNavigate();

  const handleShowPass = () => {
    setShowPass(!showPass);
  };

  const handleChange = (field, value) => {
    if (field === 'email') {
      setEmail(value);
    } else if (field === 'password') {
      setPassword(value);
    }
  };

  const handleLogin = async () => {
    const formData = new FormData();
    formData.append('email', email);
    formData.append('password', password);

    setLoading(true);

    try {
      const response = await axios.post(`${APIURL}/api/user/login`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      const { access_token, refresh_token } = response.data;
      Cookies.set('accessToken', access_token, { expires: 7 });
      Cookies.set('refreshToken', refresh_token, { expires: 7 });
      successSnackbar("Login Success!");
      navigate('/d/', { replace: true });
      return response.data;
    } catch (error) {
      if (error.response && error.response.data && error.response.data.error_messages) {
        setErrors(error?.response?.data?.error_messages);
      } else {
        setErrors({});
      }
      const errorMessage = error?.response?.data?.error_message;
      if (errorMessage) {
        errorSnackbar(errorMessage, true);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleLogin();
    }
  };

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        <Card className={styles.loginCard}>
          <Box className={styles.cardHeader}>
            <Box className={styles.headerContent}>
              <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
              <Box className={styles.headerTextContainer}>
                <Typography variant="h4" className={styles.headerTitle}>
                  Ignition
                </Typography>
                <Typography variant="body1" className={styles.headerSubtitle}>
                  Sign in to your account
                </Typography>
              </Box>
            </Box>
          </Box>

          <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 },  }}>
            <Box className={styles.googleButton}>
              <GoogleSignInButton action="Sign in" />
            </Box>

            <div className={styles.divider}>
              <span>Or sign in with email</span>
            </div>

            <Box component="form" onKeyPress={handleKeyPress} noValidate>
              <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                <div className={styles.inputField}>
                  <InputBase
                    labelText=""
                    type="email"
                    handleChange={handleChange}
                    errorText={errors.email}
                    placeholder="Email"
                    value={email}
                    keyword="email"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Iconify icon="ic:baseline-email" width={24} color={iconPrimaryColor} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
                {(errors?.email && errors?.email.length > 0) &&
                  <div className={styles.errorMessage}>{errors?.email[0]}</div>}
              </FormControl>

              <FormControl className={styles.formGroup} fullWidth>
                <div className={styles.inputField}>
                  <InputPasswordBase
                    labelText=""
                    showPass={showPass}
                    handleChange={handleChange}
                    errorText={errors.password}
                    placeholder="Password"
                    value={password}
                    name="password"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Iconify icon="material-symbols:lock" color={iconPrimaryColor} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton onClick={handleShowPass} edge="end">
                            <Iconify icon={showPass ? 'mdi:eye-off' : 'mdi:eye'} color={iconPrimaryColor} />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
                {(errors?.password && errors?.password.length > 0) &&
                  <div className={styles.errorMessage}>{errors?.password[0]}</div>}
              </FormControl>

              <Box display="flex" justifyContent="center" mt={4}>
                <Button
                  onClick={handleLogin}
                  variant="contained"
                  className={styles.submitBtn}
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign in'}
                </Button>
              </Box>
            </Box>
          </CardContent>
        </Card>

        <Box className={styles.linksContainer}>
          <a href="/forgot-password" className={styles.customeALink}>
            <Iconify icon="mdi:lock-reset" width={20} height={20} className={styles.linkIcon} />
            Forgot password?
          </a>
          <a href="/register" className={styles.customeALink}>
            <Iconify icon="mdi:account-arrow-up" width={20} height={20} className={styles.linkIcon} />
            Sign up
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default Login;
