body,
button,
input,
textarea,
select {
  font-family: 'Recursive Variable', sans-serif;
}

.mainContainer {
  padding: 24px;
  min-height: calc(100vh - 65px);
  font-family: 'Recursive Variable', sans-serif;

  * {
    font-family: 'Recursive Variable', sans-serif;
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.pageTitle {
  font-family: 'Recursive Variable';
  font-weight: 700;
  color: #333;
  margin: 0;
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;

  @media (max-width: 576px) {
    width: 100%;
    justify-content: space-between;
  }
}

.viewToggle,
.groupToggle {
  background-color: #f5f5f5;
  border-radius: 8px;

  button {
    padding: 6px;

    &.Mui-selected {
      background-color: #333;
      color: white;
    }
  }
}

.filterSection {
  display: flex;
  align-items: center;
  gap: 8px;
}

.actionButton {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 6px;

  &:hover {
    background-color: #e0e0e0;
  }

  &.activeFilter {
    background-color: #F0A500;
    color: white;
  }
}

.filterChips {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filterChip {
  background-color: #F0A500;
  color: white;
  font-family: 'Recursive Variable';
  font-size: 0.75rem;

  svg {
    color: white;
  }

  .MuiChip-deleteIcon {
    color: white;

    &:hover {
      color: #333;
    }
  }
}

.clearButton {
  color: #FF6347;
  padding: 4px;

  &:hover {
    background-color: rgba(255, 99, 71, 0.1);
  }
}

.createButton {
  background-color: #F0A500;
  color: white;
  font-family: 'Recursive Variable';
  font-weight: 600;
  border-radius: 8px;
  padding: 6px 16px;
  text-transform: none;

  &:hover {
    background-color: darken(#F0A500, 10%);
  }
}

.planGrid {
  margin-top: 16px;
}

.planCard {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  }
}

.planCardLink {
  display: block;
  padding: 24px;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.cardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.cardIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(240, 165, 0, 0.1);
  margin-right: 12px;
}

.planName {
  font-family: 'Recursive Variable';
  color: #333;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cardMeta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.875rem;
  font-family: 'Recursive Variable', sans-serif;

  span {
    font-family: 'Recursive Variable', sans-serif;
  }
}

.metaLabel {
  color: #888;
  font-weight: 600;
  margin-right: 2px;
}

.accessLevelSection {
  margin: 12px 0;
  display: flex;
  justify-content: flex-start;
}

.accessBadge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: rgba(240, 165, 0, 0.1);
  border: 1px solid rgba(240, 165, 0, 0.3);
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
}

.invitedPlanLabel {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: rgba(255, 99, 71, 0.9);
  color: white;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}



.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  text-align: center;
}

.emptyStateTitle {
  font-family: 'Recursive Variable';
  font-weight: 700;
  color: #333;
  margin-top: 24px;
  font-size: 1.5rem;
}

.emptyStateSubtitle {
  font-family: 'Recursive Variable';
  color: #666;
  margin-top: 8px;
  max-width: 400px;
}

.createEmptyButton {
  margin-top: 24px;
  background-color: #F0A500;
  color: white;
  font-family: 'Recursive Variable';
  font-weight: 600;
  border-radius: 8px;
  padding: 8px 24px;
  text-transform: none;

  &:hover {
    background-color: darken(#F0A500, 10%);
  }
}

.noDataPlanMain {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;

  .title {
    font-family: 'Recursive Variable';
    margin-top: 20px;
    color: #333;
    font-size: 1.4rem;
  }

  .subTitle {
    font-family: 'Recursive Variable';
    margin-top: 10px;
    color: #333;
    font-size: 1.125rem;
  }
}

.statusIndicator {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  span {
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
  }
}

.progressSection {
  margin-bottom: 16px;
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-family: 'Recursive Variable';

  span {
    font-family: 'Recursive Variable';
  }
}

.progressBar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.progressFill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.4s ease;
}

.sectionTitle {
  font-weight: 600;
  color: #555;
  margin-bottom: 8px;
  display: block;
  font-family: 'Recursive Variable';
}

.planCreator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: none;
  justify-content: space-between;
  font-family: 'Recursive Variable';
  margin-bottom: 8px;
}

.creatorAvatar {
  width: 28px;
  height: 28px;
}

.creatorLabel {
  color: #888;
  font-size: 0.7rem;
  display: block;
  font-family: 'Recursive Variable';
}

.creatorName {
  font-weight: 600;
  font-size: 0.8rem;
  color: #333;
  font-family: 'Recursive Variable';
}

.collaboratorsSection {
  margin-top: 12px;
}

.collaboratorsLabel {
  color: #888;
  font-size: 0.7rem;
  display: block;
  margin-bottom: 6px;
}

.descriptionSection {
  margin-bottom: 16px;
}

.tagSection {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.tagChip {
  background-color: #f0f0f0;
  color: #666;
  font-size: 0.7rem;
  height: 22px;

  &:hover {
    background-color: #e0e0e0;
  }
}

.lastUpdated {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #888;
  font-size: 0.75rem;
  margin-left: auto;

  svg {
    color: #888;
  }
}

.planGroup {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 16px;
  }
}

.groupTitle {
  font-family: 'Recursive Variable';
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #F0A500;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.planStructure {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  * {
    font-family: 'Recursive Variable', sans-serif;
  }
}

.structureItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 12px;
  background-color: #f9f9f9;
  flex: 1;
  margin: 0 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }
}

.structureIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.structureInfo {
  display: flex;
  flex-direction: column;
}

.structureLabel {
  font-size: 0.7rem;
  color: #666;
  font-family: 'Recursive Variable';
}

.structureValue {
  font-weight: 700;
  font-size: 1.2rem;
  color: #333;
  font-family: 'Recursive Variable', sans-serif;
  margin: 0;
  line-height: 1.2;
}

.taskStatusLegend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 12px;
  font-family: 'Recursive Variable';

  span {
    font-family: 'Recursive Variable';
  }
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 6px;

  span {
    font-family: 'Recursive Variable';
  }
}

.legendColor {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.milestoneChart {
  margin-bottom: 16px;
}

.milestoneChartContainer {
  display: flex;
  align-items: flex-end;
  height: 70px;
  gap: 6px;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.milestoneChartItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.milestoneChartBar {
  width: 100%;
  height: 50px;
  background-color: #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.milestoneChartFill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: height 0.5s ease;
}

.milestoneChartLabel {
  margin-top: 6px;
  font-size: 0.75rem;
  color: #666;
  font-family: 'Recursive Variable';
}

.progressValue {
  font-weight: 700;
  font-size: 1.2rem;
  color: #333;
  font-family: 'Recursive Variable', sans-serif;
  margin: 0;
  line-height: 1.2;
}

.legendText {
  font-family: 'Recursive Variable', sans-serif;
  font-size: 0.75rem;
  color: #666;
}

.popperFilterSection {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  min-width: 300px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.inputGroup {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  &:last-of-type {
    margin-bottom: 20px;
  }
}

.applyBtn {
  background-color: #F0A500;
  color: white;
  font-family: 'Recursive Variable';
  font-weight: 600;
  border-radius: 8px;
  padding: 8px 24px;
  text-transform: none;
  width: 100%;

  &:hover {
    background-color: darken(#F0A500, 10%);
  }
}

.lazyLoadTrigger {
  height: 20px;
  width: 100%;
  margin-top: 24px;
}

.loadingMoreIndicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
  margin-bottom: 24px;
  padding: 20px;
}