import axios from 'axios';
import { getHeaders } from "helpers/functions";
import { APIURL } from "helpers/constants";

export const fetchAccountInfoService = async () => {
  return await axios.get(`${APIURL}/api/user/view-profile`, { headers: getHeaders() });
};

export const updateProfileService = async (formData) => {
  return await axios.put(`${APIURL}/api/user/update-profile`, formData, { headers: getHeaders() });
};

export const fetchOtherAccountInfo = async (param) => {
  try {
    const response = await axios.get(`${APIURL}/api/other-profile/${param}`, { headers: getHeaders() });
    return response.data;
  } catch (error) {
    console.error('Error fetching account info:', error);
    throw error;
  }
};

export const fetchUserFriendDataService = async (filters, page = 1) => {
  try {
    const response = await axios.get(`${APIURL}/api/user/connect/friends`, {
      headers: getHeaders(),
      params: {
        keyword: filters.keyword || '',
        skills: filters.skills || '',
        page: page,
      },
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching friend data:', error);
    throw error;
  }
};



export const getUserSkill = async () => {
  try {
    const response = await axios.get(`${APIURL}/api/skills/user`, { headers: getHeaders() });
    return response;
  } catch (error) {
    throw new Error('Error fetching user skills: ' + error.message);
  }
}

export const addUserSkill = async (formData) => {
  try {
    const response = await axios.post(`${APIURL}/api/skills/add`,
      formData,
      { headers: getHeaders() }
    );
    return response;
  } catch (error) {
    throw error;
  }
}

export const deleteUserSkill = async (skillId) => {
  try {
    const response = await axios.delete(`${APIURL}/api/skills/delete/${skillId}`, { headers: getHeaders() });
    return response;
  } catch (error) {
    throw new Error('Error deleting user skill: ' + error.message);
  }
}
