import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Container, Box, Tabs, Tab, CircularProgress, Alert } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor, APIURL } from "helpers/constants";
import { successSnackbar, errorSnackbar } from 'components/Snackbar/index';
import { toast } from 'react-toastify';
import { getHeaders } from "helpers/functions";

// Components
import Header from './components/Header';
import Description from './components/Description';
import Statistics from './components/Statistics';
import Progress from './components/Progress';
import MilestoneList from './components/MilestoneList';
import MilestoneOverview from './components/MilestoneOverview';
import AccessManagement from './components/AccessManagement';
import ChatbotBar from './components/ChatbotBar';
import AgentTab from './components/AgentTab';

// Hooks
import usePlanData from './hooks/usePlanData';
import useViewMode from './hooks/useViewMode';

// Dialogs
import InviteDialog from './dialogs/InviteDialog';
import DeleteDialog from './dialogs/DeleteDialog';
import OptOutDialog from './dialogs/OptOutDialog';
import ConfirmDialog from './dialogs/ConfirmDialog';

// Services
import {
  updateMilestone,
  updateTask,
  updateSubtask,
  addTask,
  addSubtask,
  deleteTask,
  deleteSubtask,
  assignMembersToTask
} from '../services';

// Styles
import styles from './styles.module.scss';

const PlanDetail = () => {
  const { param } = useParams();
  const [activeTab, setActiveTab] = useState(() => {
    // Get active tab value from localStorage, default to 'overview' if not found
    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';
  });
  const [plan, setPlan] = useState(null);
  const [dialogState, setDialogState] = useState({
    invite: false,
    delete: false,
    optOut: false,
    deleteTask: false,
    deleteSubtask: false
  });
  const [selectedTaskToDelete, ] = useState(null);
  const [selectedSubtaskToDelete, ] = useState(null);

  // Custom hooks
  const {
    planInfo,
    loading,
    error,
    handleDeletePlan,
    handleOptOutPlan,
    handleInviteUser,
    calculatePlanStats,
    calculateSubtaskProgress,
    getSubtaskStatus,
    calculateTaskProgress,
    getTaskStatus,
    calculateMilestoneProgress,
    getMilestoneStatus
  } = usePlanData(param);

  const {
    viewMode,
    handleViewModeChange
  } = useViewMode();

  // Update plan state when planInfo changes
  useEffect(() => {
    if (planInfo) {
      setPlan(planInfo);
    }
  }, [planInfo]);

  // Remove active tab from localStorage when component unmounts
  useEffect(() => {
    return () => {
      localStorage.removeItem(`plan_${param}_activeTab`);
    };
  }, [param]);

  // Calculate plan statistics
  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;

  // Dialog handlers
  const openDialog = (dialogName) => {
    setDialogState(prev => ({ ...prev, [dialogName]: true }));
  };

  const closeDialog = (dialogName) => {
    setDialogState(prev => ({ ...prev, [dialogName]: false }));
  };

  // Tab change handler
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
    // Save active tab to localStorage
    localStorage.setItem(`plan_${param}_activeTab`, newValue);
  };

  // Handle switching to Agent tab from ChatbotBar
  const handleSwitchToAgent = (conversationData) => {
    console.log('PlanDetail - Switching to agent tab with data:', conversationData); // Debug log
    setActiveTab('agent');
    localStorage.setItem(`plan_${param}_activeTab`, 'agent');
    // Store the conversation data for the Agent tab to pick up
    localStorage.setItem('pending_agent_message', JSON.stringify(conversationData));
  };

  // Refresh plan data
  const refreshPlanData = async () => {
    try {
      window.location.reload(); // Simple refresh for now
    } catch (error) {
      console.error('Error refreshing plan data:', error);
    }
  };

  // Access management handlers
  const handleAddAccess = async (email, accessLevel) => {
    try {
      const response = await fetch(`${APIURL}/api/plans/${param}/access`, {
        method: 'POST',
        headers: {
          ...getHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, access_level: accessLevel })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add access');
      }

      // Refresh plan data
      await refreshPlanData();
      toast.success('Access granted successfully');
    } catch (error) {
      console.error('Error adding access:', error);
      toast.error(error.message || 'Failed to add access');
      throw error;
    }
  };

  const handleUpdateAccess = async (accessId, accessLevel, isHeadOwner = false) => {
    try {
      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {
        method: 'PUT',
        headers: {
          ...getHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          access_level: accessLevel,
          is_head_owner: isHeadOwner
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update access');
      }

      // Refresh plan data
      await refreshPlanData();
      toast.success('Access updated successfully');
    } catch (error) {
      console.error('Error updating access:', error);
      toast.error(error.message || 'Failed to update access');
      throw error;
    }
  };

  const handleRemoveAccess = async (accessId) => {
    try {
      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {
        method: 'DELETE',
        headers: getHeaders()
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to remove access');
      }

      // Refresh plan data
      await refreshPlanData();
      toast.success('Access removed successfully');
    } catch (error) {
      console.error('Error removing access:', error);
      toast.error(error.message || 'Failed to remove access');
      throw error;
    }
  };

  // Handle milestone update
  const handleUpdateMilestone = async (updatedMilestone) => {
    try {
      console.log('Updating milestone:', updatedMilestone);
      await updateMilestone(updatedMilestone);

      // Update local state
      const updatedPlan = { ...plan };
      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);

      if (milestoneIndex !== -1) {
        updatedPlan.milestones[milestoneIndex] = {
          ...updatedPlan.milestones[milestoneIndex],
          ...updatedMilestone
        };
        setPlan(updatedPlan);
      }

      successSnackbar('Milestone updated successfully');
    } catch (error) {
      console.error('Error updating milestone:', error);
      errorSnackbar('Failed to update milestone');
    }
  };

  // Handle task update
  const handleUpdateTask = async (updatedTask) => {
    try {
      console.log('Updating task:', updatedTask);
      await updateTask(updatedTask);

      // Update local state
      const updatedPlan = { ...plan };
      const milestoneIndex = updatedPlan.milestones.findIndex(m =>
        m.tasks && m.tasks.some(t => t.id === updatedTask.id)
      );

      if (milestoneIndex !== -1) {
        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);

        if (taskIndex !== -1) {
          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {
            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],
            ...updatedTask
          };
          setPlan(updatedPlan);
        }
      }

      successSnackbar('Task updated successfully');
    } catch (error) {
      console.error('Error updating task:', error);
      errorSnackbar('Failed to update task');
    }
  };

  // Handle subtask update
  const handleUpdateSubtask = async (updatedSubtask) => {
    try {
      console.log('Updating subtask:', updatedSubtask);

      // Call API to update subtask
      await updateSubtask(updatedSubtask);

      // Create a copy of current plan to update
      const updatedPlan = { ...plan };

      // Find the task containing the subtask
      let taskFound = false;

      // Update subtask in state
      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];
          if (!task.subtasks) continue;

          // Update subtask in task
          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);
          if (subtaskIndex !== -1) {
            // Update subtask
            task.subtasks[subtaskIndex] = {
              ...task.subtasks[subtaskIndex],
              ...updatedSubtask
            };
            taskFound = true;
            break;
          }
        }

        if (taskFound) break;
      }

      // Update state with new plan
      setPlan(updatedPlan);

      successSnackbar('Subtask updated successfully');
    } catch (error) {
      console.error('Error updating subtask:', error);
      errorSnackbar('Failed to update subtask');
    }
  };

  // Handle add task
  const handleAddTask = async (newTask) => {
    try {
      console.log('Adding new task:', newTask);
      const response = await addTask(newTask);
      console.log('Add task response:', response);

      // Update local state
      const updatedPlan = { ...plan };

      // Find milestone to add new task
      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);

      if (milestoneIndex !== -1) {
        // Add new task to milestone
        if (!updatedPlan.milestones[milestoneIndex].tasks) {
          updatedPlan.milestones[milestoneIndex].tasks = [];
        }

        // Add the new task with data from response
        const taskToAdd = response.data || {
          ...newTask,
          id: Date.now(), // Temporary ID if response doesn't provide one
          subtasks: []
        };

        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);
        setPlan(updatedPlan);
      }

      successSnackbar('Task added successfully');
    } catch (error) {
      console.error('Error adding task:', error);
      errorSnackbar('Failed to add task');
    }
  };

  // Handle add subtask
  const handleAddSubtask = async (newSubtask) => {
    try {
      console.log('Adding new subtask:', newSubtask);
      const response = await addSubtask(newSubtask);
      console.log('Add subtask response:', response);

      // Update local state
      const updatedPlan = { ...plan };

      // Find task to add new subtask
      let taskFound = false;

      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];

          if (task.slug === newSubtask.task) {
            // Add new subtask to task
            if (!task.subtasks) {
              task.subtasks = [];
            }

            // Add the new subtask with data from response
            const subtaskToAdd = response.data || {
              ...newSubtask,
              id: Date.now() // Temporary ID if response doesn't provide one
            };

            task.subtasks.push(subtaskToAdd);
            taskFound = true;
            break;
          }
        }

        if (taskFound) break;
      }

      setPlan(updatedPlan);
      successSnackbar('Subtask added successfully');
    } catch (error) {
      console.error('Error adding subtask:', error);
      errorSnackbar('Failed to add subtask');
    }
  };

  // Handle delete task
  const handleDeleteTask = async (taskToDelete) => {
    try {
      console.log('Deleting task:', taskToDelete);
      await deleteTask(taskToDelete.slug);

      // Update local state
      const updatedPlan = { ...plan };

      // Find milestone containing the task to delete
      const milestoneIndex = updatedPlan.milestones.findIndex(m =>
        m.tasks && m.tasks.some(t => t.id === taskToDelete.id)
      );

      if (milestoneIndex !== -1) {
        // Filter out the task to delete
        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(
          t => t.id !== taskToDelete.id
        );

        setPlan(updatedPlan);
      }

      successSnackbar('Task deleted successfully');
    } catch (error) {
      console.error('Error deleting task:', error);
      errorSnackbar('Failed to delete task');
    }
  };

  // Handle delete subtask
  const handleDeleteSubtask = async (subtaskToDelete) => {
    try {
      console.log('Deleting subtask:', subtaskToDelete);
      await deleteSubtask(subtaskToDelete.slug);

      // Update local state
      const updatedPlan = { ...plan };

      // Find task containing the subtask to delete
      let taskFound = false;

      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];
          if (!task.subtasks) continue;

          // Filter out the subtask to delete
          const originalLength = task.subtasks.length;
          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);

          if (task.subtasks.length < originalLength) {
            taskFound = true;
            break;
          }
        }

        if (taskFound) break;
      }

      setPlan(updatedPlan);
      toast.success('Subtask deleted successfully');
    } catch (error) {
      console.error('Error deleting subtask:', error);
      toast.error('Failed to delete subtask');
    }
  };

  // Handle assign members to task
  const handleAssignMembers = async (taskToAssign, memberIds) => {
    try {
      console.log('Assigning members to task:', taskToAssign, memberIds);
      await assignMembersToTask(taskToAssign.slug, memberIds);

      // Update local state
      const updatedPlan = { ...plan };

      // Find the task to update
      let taskFound = false;

      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];
          if (task.id === taskToAssign.id) {
            // Update assignees
            // Find user objects for the selected member IDs
            const assignedMembers = memberIds.map(memberId => {
              // Check if it's the plan owner
              if (planInfo.owner && planInfo.owner.id === memberId) {
                return {
                  id: planInfo.owner.id,
                  first_name: planInfo.owner.first_name,
                  last_name: planInfo.owner.last_name,
                  email: planInfo.owner.email,
                  avatar: planInfo.owner.avatar
                };
              }

              // Check in invited users
              const invitedUser = planInfo.invited_users?.find(
                user => user.invited_user_info && user.invited_user_info.id === memberId
              );

              if (invitedUser) {
                return {
                  id: invitedUser.invited_user_info.id,
                  first_name: invitedUser.invited_user_info.first_name,
                  last_name: invitedUser.invited_user_info.last_name,
                  email: invitedUser.email,
                  avatar: invitedUser.invited_user_info.avatar
                };
              }

              return null;
            }).filter(member => member !== null);

            task.assignees = assignedMembers;
            taskFound = true;
            break;
          }
        }

        if (taskFound) break;
      }

      setPlan(updatedPlan);
      toast.success('Members assigned successfully');
    } catch (error) {
      console.error('Error assigning members to task:', error);
      toast.error('Failed to assign members to task');
    }
  };

  if (error) {
    return (
      <Container className={styles.container}>
        <Alert severity="error" sx={{ mt: 4 }}>
          An error occurred while loading plan information. Please try again later.
        </Alert>
      </Container>
    );
  }

  return (
    <Container
      maxWidth="lg"
      className={styles.container}
      sx={{
        padding: '20px',
        minHeight: 'calc(100vh - 65px)',
        fontFamily: '"Recursive Variable", sans-serif'
      }}
    >
      {loading ? (
        <Box className={styles.loadingContainer}>
          <CircularProgress size={60} sx={{ color: mainYellowColor }} />
        </Box>
      ) : (
        <>
          {/* Header Section */}
          <Header
            planInfo={planInfo}
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
            onOpenInviteDialog={() => openDialog('invite')}
            onOpenDeleteDialog={() => openDialog('delete')}
            onOpenOptOutDialog={() => openDialog('optOut')}
          />

          {/* Tabs Navigation */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 0.5, mt: -1 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                minHeight: '36px',
                '& .MuiTab-root': {
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.9rem',
                  minWidth: 'auto',
                  minHeight: '36px',
                  px: 2,
                  py: 0.5,
                  fontFamily: '"Recursive Variable", sans-serif'
                },
                '& .Mui-selected': {
                  color: `${mainYellowColor} !important`,
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: mainYellowColor,
                  height: '2px'
                }
              }}
            >
              <Tab
                icon={<Iconify icon="material-symbols:dashboard" width={16} height={16} />}
                iconPosition="start"
                label="Overview"
                value="overview"
                sx={{ gap: '4px' }}
              />
              <Tab
                icon={<Iconify icon="material-symbols:view-list" width={16} height={16} />}
                iconPosition="start"
                label="Project Details"
                value="milestones"
                sx={{ gap: '4px' }}
              />
              <Tab
                icon={<Iconify icon="mdi:robot" width={16} height={16} />}
                iconPosition="start"
                label="Agent"
                value="agent"
                sx={{ gap: '4px' }}
              />
              {planInfo?.user_access_level?.access_level === 'owner' && (
                <Tab
                  icon={<Iconify icon="material-symbols:security" width={16} height={16} />}
                  iconPosition="start"
                  label="Access"
                  value="access"
                  sx={{ gap: '4px' }}
                />
              )}
            </Tabs>
          </Box>

          {/* Tab Content */}
          <Box className={styles.tabContent}>
            {activeTab === 'overview' && (
              <Box className={styles.overviewTab} sx={{ gap: 0.5 }}>
                <Description planInfo={planInfo} />
                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 1, mb: 0.5 }}>
                  <Statistics stats={stats} />
                  <Progress stats={stats} />
                </Box>
                {planInfo?.user_access_level?.access_level === 'owner' && (
                  <AccessManagement
                    planInfo={planInfo}
                    userAccessLevel={planInfo?.user_access_level}
                    onAddAccess={handleAddAccess}
                    onUpdateAccess={handleUpdateAccess}
                    onRemoveAccess={handleRemoveAccess}
                  />
                )}
                <MilestoneOverview
                  milestones={planInfo?.milestones}
                  calculateMilestoneProgress={calculateMilestoneProgress}
                  getMilestoneStatus={getMilestoneStatus}
                  calculateTaskProgress={calculateTaskProgress}
                  getTaskStatus={getTaskStatus}
                  calculateSubtaskProgress={calculateSubtaskProgress}
                  getSubtaskStatus={getSubtaskStatus}
                />
              </Box>
            )}

            {activeTab === 'milestones' && (
              <>
                <ChatbotBar
                  planInfo={planInfo}
                  onPlanUpdate={(updatedPlan) => {
                    // Handle plan updates from AI agent
                    console.log('Plan updated by AI:', updatedPlan);
                  }}
                  onSwitchToAgent={handleSwitchToAgent}
                />
                <MilestoneList
                  milestones={planInfo?.milestones}
                  viewMode={viewMode}
                  compact={false}
                  showSubtasks={true}
                  calculateMilestoneProgress={calculateMilestoneProgress}
                  getMilestoneStatus={getMilestoneStatus}
                  calculateTaskProgress={calculateTaskProgress}
                  getTaskStatus={getTaskStatus}
                  calculateSubtaskProgress={calculateSubtaskProgress}
                  getSubtaskStatus={getSubtaskStatus}
                  onUpdateMilestone={handleUpdateMilestone}
                  onUpdateTask={handleUpdateTask}
                  onUpdateSubtask={handleUpdateSubtask}
                  onAddTask={handleAddTask}
                  onAddSubtask={handleAddSubtask}
                  onDeleteTask={handleDeleteTask}
                  onDeleteSubtask={handleDeleteSubtask}
                  onAssignMembers={handleAssignMembers}
                  invitedUsers={planInfo?.invited_users || []}
                  planOwner={planInfo?.owner}
                />
              </>
            )}

            {activeTab === 'agent' && (
              <AgentTab
                planInfo={planInfo}
                onPlanUpdate={() => {
                  // Refresh plan data when AI agent makes changes
                  console.log('Plan updated by AI agent, refreshing data...');
                  refreshPlanData();
                }}
              />
            )}

            {activeTab === 'access' && planInfo?.user_access_level?.access_level === 'owner' && (
              <AccessManagement
                planInfo={planInfo}
                userAccessLevel={planInfo?.user_access_level}
                onAddAccess={handleAddAccess}
                onUpdateAccess={handleUpdateAccess}
                onRemoveAccess={handleRemoveAccess}
              />
            )}
          </Box>
        </>
      )}

      {/* Dialogs */}
      <InviteDialog
        open={dialogState.invite}
        onClose={() => closeDialog('invite')}
        onInvite={handleInviteUser}
        planInfo={planInfo}
      />

      <DeleteDialog
        open={dialogState.delete}
        onClose={() => closeDialog('delete')}
        onDelete={handleDeletePlan}
      />

      <OptOutDialog
        open={dialogState.optOut}
        onClose={() => closeDialog('optOut')}
        onOptOut={handleOptOutPlan}
      />

      <ConfirmDialog
        open={dialogState.deleteTask}
        onClose={() => setDialogState(prev => ({ ...prev, deleteTask: false }))}
        onConfirm={() => handleDeleteTask(selectedTaskToDelete)}
        title="Delete Task"
        description={`Are you sure you want to delete the task "${selectedTaskToDelete?.name}"? This action cannot be undone.`}
      />

      <ConfirmDialog
        open={dialogState.deleteSubtask}
        onClose={() => setDialogState(prev => ({ ...prev, deleteSubtask: false }))}
        onConfirm={() => handleDeleteSubtask(selectedSubtaskToDelete)}
        title="Delete Subtask"
        description={`Are you sure you want to delete the subtask "${selectedSubtaskToDelete?.name}"? This action cannot be undone.`}
      />
    </Container>
  );
};

export default PlanDetail;
