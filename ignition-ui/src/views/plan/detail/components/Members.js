import React, { useEffect, useMemo, useState } from 'react';
import { Box, Typography, Paper, Avatar, Chip, Button, Grid } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from '../styles.module.scss';

const Members = ({ invitedUsers, planOwner, onInvite, expanded = false }) => {
  const [showAllMembers, setShowAllMembers] = useState(expanded);

  // Ensure invitedUsers is an array using useMemo
  const safeInvitedUsers = useMemo(() => {
    return Array.isArray(invitedUsers) ? invitedUsers : [];
  }, [invitedUsers]);

  // Calculate total members (including owner)
  const totalMembers = safeInvitedUsers.length + 1;

  // Log to check data
  useEffect(() => {
    console.log('Members component - invitedUsers:', invitedUsers);
    console.log('Members component - safeInvitedUsers:', safeInvitedUsers);
    console.log('Members component - planOwner:', planOwner);
  }, [invitedUsers, safeInvitedUsers, planOwner]);

  const handleViewAllMembers = () => {
    setShowAllMembers(true);
  };

  return (
    <Paper
      elevation={0}
      className={styles.membersCard}
      sx={{
        borderRadius: '12px',
        mb: 0.5,
        py: 1,
        px: 1.5
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Iconify icon="material-symbols:people" width={20} height={20} color={mainYellowColor} />
          <Typography
            variant="h6"
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600,
              fontSize: '1.2rem',
              color: '#555',
              margin: 0
            }}
          >
            Members ({totalMembers})
          </Typography>
        </Box>

        <Button
          onClick={onInvite}
          variant="contained"
          startIcon={<Iconify icon="material-symbols:person-add" width={14} height={14} />}
          size="small"
          sx={{
            backgroundColor: mainYellowColor,
            color: '#333',
            fontWeight: 600,
            textTransform: 'none',
            borderRadius: '6px',
            fontFamily: '"Recursive Variable", sans-serif',
            padding: '2px 8px',
            fontSize: '0.9rem',
            minWidth: '60px',
            height: '24px',
            '&:hover': {
              backgroundColor: '#e0a800'
            }
          }}
        >
          Invite
        </Button>
      </Box>

      <Grid container spacing={1}>
        {/* Owner */}
        {planOwner && (
          <Grid item xs={12} sm={6}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                p: 0.75,
                borderRadius: '6px',
                backgroundColor: '#f9f9f9',
                height: '100%'
              }}
            >
              <Avatar
                src={planOwner?.avatar}
                alt={planOwner?.first_name}
                sx={{
                  width: 32,
                  height: 32,
                  border: `2px solid ${mainYellowColor}`
                }}
              />
              <Box sx={{ flexGrow: 1, minWidth: 0, overflow: 'hidden' }}>
                <Typography variant="body1" sx={{ fontWeight: 600, fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', lineHeight: 1.2, color: '#333', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {planOwner?.first_name} {planOwner?.last_name}
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.8rem', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {planOwner?.email}
                </Typography>
              </Box>
              <Chip
                label="Owner"
                size="small"
                sx={{
                  backgroundColor: `${mainYellowColor}20`,
                  color: '#333',
                  fontWeight: 600,
                  fontFamily: '"Recursive Variable", sans-serif',
                  height: '20px',
                  '& .MuiChip-label': {
                    padding: '0 6px',
                    fontSize: '0.75rem'
                  }
                }}
              />
            </Box>
          </Grid>
        )}

        {/* Invited Users */}
        {safeInvitedUsers.length > 0 ? (
          safeInvitedUsers.slice(0, showAllMembers ? safeInvitedUsers.length : (safeInvitedUsers.length > 3 ? 3 : safeInvitedUsers.length)).map((user, index) => {
            // Check the structure of the user data, which can be from the API or from the new API
            let userInfo;
            let userEmail;
            let userStatus;

            if (user.invited_user_info) {
              // Structure from the old API invitedUsers
              userInfo = user.invited_user_info;
              userEmail = user.email;
              userStatus = user.accepted;
            } else if (user.invitations) {
              // Structure from the new API - nested data case
              userInfo = user;
              userEmail = user.email;
              userStatus = user.invitations?.[0]?.accepted || 0;
            } else {
              // Full user object case
              userInfo = user;
              userEmail = user.email;
              userStatus = 1; // Default is joined
            }

            return (
              <Grid item xs={12} sm={6} key={`member-${index}-${userInfo?.id || index}`}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    p: 0.75,
                    borderRadius: '6px',
                    backgroundColor: '#f9f9f9',
                    opacity: userStatus === 1 ? 1 : 0.7,
                    height: '100%'
                  }}
                >
                  <Avatar
                    src={userInfo?.avatar}
                    alt={userInfo?.first_name || userEmail}
                    sx={{ width: 32, height: 32 }}
                  />
                  <Box sx={{ flexGrow: 1, minWidth: 0, overflow: 'hidden' }}>
                    <Typography variant="body1" sx={{ fontWeight: 600, fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', lineHeight: 1.2, color: '#333', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {userInfo?.first_name
                        ? `${userInfo.first_name} ${userInfo.last_name || ''}`
                        : userEmail}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.8rem', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {userEmail}
                    </Typography>
                  </Box>
                  <Chip
                    label={userStatus === 1 ? "Joined" : "Pending"}
                    size="small"
                    sx={{
                      backgroundColor: userStatus === 1 ? '#4CAF5020' : '#FFC10720',
                      color: userStatus === 1 ? '#4CAF50' : '#FFC107',
                      fontWeight: 600,
                      fontFamily: '"Recursive Variable", sans-serif',
                      height: '20px',
                      '& .MuiChip-label': {
                        padding: '0 6px',
                        fontSize: '0.75rem'
                      }
                    }}
                  />
                </Box>
              </Grid>
            );
          })
        ) : (
          <Grid item xs={12}>
            <Typography variant="body2" sx={{ color: '#666', fontStyle: 'italic', p: 1, textAlign: 'center' }}>
              No members invited yet
            </Typography>
          </Grid>
        )}
      </Grid>

      {!showAllMembers && safeInvitedUsers.length > 3 && (
        <Button
          onClick={handleViewAllMembers}
          variant="text"
          sx={{
            color: mainYellowColor,
            fontWeight: 600,
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontSize: '0.9rem',
            padding: '4px 0',
            minHeight: '24px',
            mt: 1,
            display: 'block',
            ml: 'auto'
          }}
        >
          View all {safeInvitedUsers.length} members
        </Button>
      )}
    </Paper>
  );
};

export default Members;
