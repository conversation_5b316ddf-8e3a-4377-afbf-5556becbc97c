import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Avatar,
  Divider,
  CircularProgress,
  Chip,
  Tooltip
} from '@mui/material';
import { useLocation } from 'react-router-dom';
import Iconify from 'components/Iconify/index';
import { mainYellowColor, APIURL } from "helpers/constants";
import { getHeaders } from "helpers/functions";
import axios from 'axios';

const AgentTab = ({ planInfo, onPlanUpdate }) => {
  const [conversations, setConversations] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const [error, setError] = useState(null);
  const location = useLocation();

  // Load conversations from localStorage
  useEffect(() => {
    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');
    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);
    setConversations(planConversations);

    // Check for pending message from ChatbotBar
    const pendingMessage = localStorage.getItem('pending_agent_message');
    if (pendingMessage) {
      try {
        const messageData = JSON.parse(pendingMessage);
        if (messageData.planInfo?.id === planInfo?.id) {
          handleSendMessage(messageData.message);
        }
        // Clear the pending message
        localStorage.removeItem('pending_agent_message');
      } catch (error) {
        console.error('Error processing pending message:', error);
        localStorage.removeItem('pending_agent_message');
      }
    }

    // If coming from chatbot bar via navigation state, add the initial message
    if (location.state?.message) {
      handleSendMessage(location.state.message);
    }
  }, [planInfo?.id, location.state]);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversations]);

  const executeAction = async (action, originalMessage) => {
    try {
      const { type, entities } = action;

      if (type === 'add_milestone') {
        // Extract milestone name from the message
        let milestoneName = 'New Milestone';

        // Try to extract from quoted text first
        if (entities.taskNames && entities.taskNames.length > 0) {
          milestoneName = entities.taskNames[0];
        } else {
          // Try to extract from common patterns
          const patterns = [
            /add.*milestone.*["']([^"']+)["']/i,
            /create.*milestone.*["']([^"']+)["']/i,
            /new milestone.*["']([^"']+)["']/i,
            /milestone.*["']([^"']+)["']/i,
            /add.*milestone.*(?:for|about|called)\s+([^.!?]+)/i,
            /create.*milestone.*(?:for|about|called)\s+([^.!?]+)/i
          ];

          for (const pattern of patterns) {
            const match = originalMessage.match(pattern);
            if (match && match[1]) {
              milestoneName = match[1].trim();
              break;
            }
          }
        }

        const response = await axios.post(
          `${APIURL}/api/assistant/plan-action`,
          {
            action: 'add_milestone',
            plan_slug: planInfo.slug,
            data: {
              name: milestoneName,
              description: `Milestone created by AI agent based on: "${originalMessage}"`
            },
            message: originalMessage
          },
          { headers: getHeaders() }
        );

        return {
          success: true,
          message: `✅ Successfully created milestone "${milestoneName}"!

The new milestone has been added to your project. You can now:
• Add tasks to this milestone
• Set specific goals and deadlines
• Track progress as you work

Would you like me to add some initial tasks to this milestone?`,
          data: response.data
        };
      }

      // Handle other action types here...
      return {
        success: false,
        message: `I understand you want to ${action.description.toLowerCase()}, but I'm still learning how to do that. For now, I can help you add milestones to your project. What else can I help you with?`
      };

    } catch (error) {
      console.error('Error executing action:', error);
      return {
        success: false,
        message: `Sorry, I encountered an error while trying to ${action.description.toLowerCase()}: ${error.response?.data?.error || error.message}. Please try again or rephrase your request.`
      };
    }
  };

  const handleSendMessage = async (messageText = currentMessage) => {
    if (!messageText.trim() || isLoading) return;

    setIsLoading(true);
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: messageText.trim(),
      timestamp: new Date().toISOString()
    };

    const newConversations = [...conversations, userMessage];
    setConversations(newConversations);
    setCurrentMessage('');

    try {
      // Extract actions from the message
      const actions = extractActions(messageText);
      let aiResponse;

      if (actions.length > 0) {
        // Execute the first action (for now, we'll handle one action at a time)
        const action = actions[0];
        const result = await executeAction(action, messageText);

        aiResponse = {
          id: Date.now() + 1,
          type: 'assistant',
          content: result.message,
          timestamp: new Date().toISOString(),
          actions: actions,
          actionResult: result
        };

        // If the action was successful and modified the plan, trigger a refresh
        if (result.success && onPlanUpdate) {
          onPlanUpdate();
        }
      } else {
        // Generate a contextual response without actions
        aiResponse = {
          id: Date.now() + 1,
          type: 'assistant',
          content: generateAIResponse(messageText, planInfo),
          timestamp: new Date().toISOString(),
          actions: []
        };
      }

      const updatedConversations = [...newConversations, aiResponse];
      setConversations(updatedConversations);

      // Save to localStorage
      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');
      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);
      const planConversations = updatedConversations.map(conv => ({
        ...conv,
        planId: planInfo?.id,
        planName: planInfo?.name
      }));
      
      localStorage.setItem('agent_conversations', JSON.stringify([
        ...otherPlanConversations,
        ...planConversations
      ]));

    } catch (error) {
      console.error('Error processing message:', error);
      const errorResponse = {
        id: Date.now() + 1,
        type: 'assistant',
        content: `Sorry, I encountered an error while processing your request: ${error.message || 'Unknown error'}. Please try again.`,
        timestamp: new Date().toISOString(),
        isError: true
      };
      setConversations([...newConversations, errorResponse]);
      setError(error.message || 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const generateAIResponse = (message, planInfo) => {
    const lowerMessage = message.toLowerCase();
    const milestones = planInfo?.milestones || [];
    const totalTasks = milestones.reduce((acc, milestone) => acc + (milestone.tasks?.length || 0), 0);
    const totalSubtasks = milestones.reduce((acc, milestone) =>
      acc + milestone.tasks?.reduce((taskAcc, task) => taskAcc + (task.subtasks?.length || 0), 0) || 0, 0);

    // Analyze project context
    const projectContext = {
      name: planInfo?.name || 'your project',
      milestoneCount: milestones.length,
      taskCount: totalTasks,
      subtaskCount: totalSubtasks,
      milestoneNames: milestones.map(m => m.name).slice(0, 3), // First 3 milestone names
    };

    // Advanced intent recognition and response generation
    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {
      return `I can help you add a new milestone to "${projectContext.name}". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.

To add a new milestone, I'll need:
1. **Milestone name** - What should we call it?
2. **Description** - What's the main objective?
3. **Position** - Should it come before/after a specific milestone?
4. **Tasks** - Any initial tasks to include?

Please provide these details, or just tell me the milestone name and I'll help structure the rest!`;
    }

    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {
      const progressDetails = milestones.map(milestone => {
        const taskCount = milestone.tasks?.length || 0;
        const completedTasks = milestone.tasks?.filter(task => task.status === 'completed')?.length || 0;
        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;
      }).join('\n');

      return `Here's your project progress for "${projectContext.name}":

📊 **Overall Statistics:**
• ${projectContext.milestoneCount} milestones
• ${projectContext.taskCount} total tasks
• ${projectContext.subtaskCount} total subtasks

📋 **Milestone Progress:**
${progressDetails}

Would you like me to:
• Show detailed progress for a specific milestone?
• Identify overdue or at-risk tasks?
• Suggest next actions to move the project forward?`;
    }

    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {
      const availableTasks = milestones.flatMap(milestone =>
        milestone.tasks?.filter(task => task.status !== 'completed').map(task =>
          `"${task.name}" in ${milestone.name}`
        ) || []
      ).slice(0, 5);

      return `I can mark tasks as completed for you! Here are some pending tasks I found:

${availableTasks.map(task => `• ${task}`).join('\n')}

To mark a task as complete, just tell me:
• "Mark [task name] as completed"
• "Complete the [task name] task"
• Or simply "Done with [task name]"

Which task would you like to mark as completed?`;
    }

    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {
      if (lowerMessage.includes('task')) {
        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:

${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\n')}

To add a task, tell me:
• **Which milestone** to add it to
• **Task name** and description
• **Any subtasks** to include

Example: "Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings"

What task would you like to add?`;
      }

      return `I can help you add new content to "${projectContext.name}". I can create:

🎯 **Milestones** - Major project phases
📋 **Tasks** - Specific work items within milestones
✅ **Subtasks** - Detailed steps for tasks
📝 **Descriptions** - Enhanced details for any item

What would you like to add? Just describe it naturally, like:
• "Add a milestone for user testing"
• "Create a task for database setup in the development milestone"
• "Add subtasks for the API integration task"`;
    }

    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {
      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.

I can remove:
• **Tasks** that are no longer needed
• **Subtasks** that are redundant
• **Completed items** to clean up the project
• **Duplicate entries**

⚠️ **Note:** I cannot delete milestones as they're core to your project structure.

What would you like to remove? Please be specific about the item name and location.`;
    }

    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {
      return `I can update various aspects of your project "${projectContext.name}":

📝 **Content Updates:**
• Task and subtask descriptions
• Milestone objectives
• Due dates and priorities
• Task assignments

🔄 **Status Changes:**
• Mark items as in-progress, completed, or blocked
• Update milestone phases
• Change task priorities

📊 **Structural Changes:**
• Move tasks between milestones
• Reorder items
• Split large tasks into smaller ones

What would you like to update? Describe the change you want to make.`;
    }

    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {
      return `I'm your AI Project Assistant for "${projectContext.name}"! Here's what I can do:

🎯 **Project Management:**
• Add/remove tasks, subtasks, and milestones
• Update descriptions, statuses, and priorities
• Mark items as completed or in-progress
• Move tasks between milestones

📊 **Project Analysis:**
• Show progress reports and statistics
• Identify bottlenecks and overdue items
• Suggest next actions and optimizations
• Generate project summaries

🔍 **Smart Search:**
• Find specific tasks or milestones
• Filter by status, assignee, or due date
• Locate related items across the project

💡 **Recommendations:**
• Suggest task breakdowns
• Recommend milestone structures
• Identify missing dependencies
• Propose timeline optimizations

Just tell me what you want to do in natural language - I'll understand and help you get it done!`;
    }

    // Default intelligent response
    return `I understand you want to "${message}".

Based on your project "${projectContext.name}" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:

🎯 **Quick Actions:**
• "Show me project progress"
• "Add a new task to [milestone name]"
• "Mark [task name] as completed"
• "Update the description for [item name]"

💡 **Smart Suggestions:**
• "What should I work on next?"
• "Show me overdue items"
• "Help me organize this milestone"
• "Create a timeline for this project"

What specific action would you like me to take? I'm here to make managing your project easier!`;
  };

  const extractActions = (message) => {
    const actions = [];
    const lowerMessage = message.toLowerCase();

    // Enhanced action extraction with context
    const actionPatterns = [
      {
        pattern: /(complete|done|finish|mark.*complete)/,
        type: 'complete_task',
        confidence: 0.9,
        description: 'Mark task as completed'
      },
      {
        pattern: /(add|create|new).*milestone/,
        type: 'add_milestone',
        confidence: 0.9,
        description: 'Add new milestone'
      },
      {
        pattern: /(add|create|new).*task/,
        type: 'add_task',
        confidence: 0.9,
        description: 'Add new task'
      },
      {
        pattern: /(add|create|new).*subtask/,
        type: 'add_subtask',
        confidence: 0.9,
        description: 'Add new subtask'
      },
      {
        pattern: /(delete|remove).*task/,
        type: 'delete_task',
        confidence: 0.8,
        description: 'Delete task'
      },
      {
        pattern: /(update|change|edit|modify)/,
        type: 'update_item',
        confidence: 0.8,
        description: 'Update item details'
      },
      {
        pattern: /(progress|status|overview)/,
        type: 'show_progress',
        confidence: 0.9,
        description: 'Show project progress'
      },
      {
        pattern: /(move|transfer).*task/,
        type: 'move_task',
        confidence: 0.8,
        description: 'Move task between milestones'
      },
      {
        pattern: /(assign|delegate)/,
        type: 'assign_task',
        confidence: 0.8,
        description: 'Assign task to team member'
      },
      {
        pattern: /(due date|deadline|schedule)/,
        type: 'set_deadline',
        confidence: 0.8,
        description: 'Set or update due date'
      }
    ];

    // Extract entities (task names, milestone names, etc.)
    const entities = {
      taskNames: [],
      milestoneNames: [],
      dates: [],
      priorities: []
    };

    // Look for quoted text (likely task/milestone names)
    const quotedText = message.match(/"([^"]+)"/g);
    if (quotedText) {
      entities.taskNames = quotedText.map(q => q.replace(/"/g, ''));
    }

    // Look for date patterns
    const datePatterns = message.match(/\b\d{1,2}\/\d{1,2}\/\d{4}\b|\b\d{1,2}-\d{1,2}-\d{4}\b|tomorrow|today|next week|next month/gi);
    if (datePatterns) {
      entities.dates = datePatterns;
    }

    // Look for priority keywords
    const priorityPatterns = message.match(/\b(high|low|medium|urgent|critical|normal)\s*priority\b/gi);
    if (priorityPatterns) {
      entities.priorities = priorityPatterns;
    }

    // Match actions against patterns
    actionPatterns.forEach(({ pattern, type, confidence, description }) => {
      if (pattern.test(lowerMessage)) {
        actions.push({
          type,
          confidence,
          description,
          entities: entities,
          originalMessage: message
        });
      }
    });

    return actions;
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '12px 12px 0 0',
          border: '1px solid #f0f0f0',
          borderBottom: 'none',
          backgroundColor: '#fafafa'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              backgroundColor: mainYellowColor,
              width: 40,
              height: 40
            }}
          >
            <Iconify icon="mdi:robot" width={24} height={24} color="#fff" />
          </Avatar>
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333'
              }}
            >
              AI Project Agent
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              Managing: {planInfo?.name}
            </Typography>
          </Box>
          <Chip
            label="Beta"
            size="small"
            sx={{
              backgroundColor: `${mainYellowColor}20`,
              color: mainYellowColor,
              fontWeight: 600,
              ml: 'auto'
            }}
          />
        </Box>
      </Paper>

      {/* Messages Area */}
      <Paper
        elevation={0}
        sx={{
          flex: 1,
          border: '1px solid #f0f0f0',
          borderTop: 'none',
          borderBottom: 'none',
          overflow: 'auto',
          p: 2,
          backgroundColor: '#fff'
        }}
      >
        {conversations.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              textAlign: 'center'
            }}
          >
            <Avatar
              sx={{
                backgroundColor: `${mainYellowColor}20`,
                width: 60,
                height: 60,
                mb: 2
              }}
            >
              <Iconify icon="mdi:robot" width={32} height={32} color={mainYellowColor} />
            </Avatar>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333',
                mb: 1
              }}
            >
              Welcome to AI Project Agent
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif',
                maxWidth: 400
              }}
            >
              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!
            </Typography>
          </Box>
        ) : (
          <Box>
            {conversations.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                  mb: 2
                }}
              >
                <Box
                  sx={{
                    maxWidth: '70%',
                    display: 'flex',
                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: 1
                  }}
                >
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor
                    }}
                  >
                    <Iconify
                      icon={message.type === 'user' ? "material-symbols:person" : "mdi:robot"}
                      width={18}
                      height={18}
                      color={message.type === 'user' ? '#666' : '#fff'}
                    />
                  </Avatar>
                  <Box>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        borderRadius: '12px',
                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',
                        color: message.type === 'user' ? '#fff' : '#333',
                        border: message.isError ? '1px solid #f44336' : 'none'
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: '"Recursive Variable", sans-serif',
                          lineHeight: 1.5,
                          whiteSpace: 'pre-line'
                        }}
                      >
                        {message.content}
                      </Typography>

                      {/* Quick Action Buttons for AI responses */}
                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (
                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {message.actions.slice(0, 3).map((action, actionIndex) => (
                            <Chip
                              key={actionIndex}
                              label={action.description}
                              size="small"
                              onClick={() => {
                                // Handle quick action
                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);
                                if (inputRef.current) {
                                  inputRef.current.focus();
                                }
                              }}
                              sx={{
                                backgroundColor: '#fff',
                                border: `1px solid ${mainYellowColor}`,
                                color: mainYellowColor,
                                fontSize: '0.7rem',
                                height: '24px',
                                cursor: 'pointer',
                                '&:hover': {
                                  backgroundColor: `${mainYellowColor}10`
                                }
                              }}
                            />
                          ))}
                        </Box>
                      )}
                    </Paper>
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#999',
                        fontFamily: '"Recursive Variable", sans-serif',
                        fontSize: '0.7rem',
                        mt: 0.5,
                        display: 'block',
                        textAlign: message.type === 'user' ? 'right' : 'left'
                      }}
                    >
                      {formatTimestamp(message.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))}
            {isLoading && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: mainYellowColor
                    }}
                  >
                    <Iconify icon="mdi:robot" width={18} height={18} color="#fff" />
                  </Avatar>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 1.5,
                      borderRadius: '12px',
                      backgroundColor: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />
                    <Typography
                      variant="body2"
                      sx={{
                        fontFamily: '"Recursive Variable", sans-serif',
                        color: '#666'
                      }}
                    >
                      Thinking...
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            )}
            <div ref={messagesEndRef} />
          </Box>
        )}
      </Paper>

      {/* Input Area */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '0 0 12px 12px',
          border: '1px solid #f0f0f0',
          borderTop: 'none'
        }}
      >
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            inputRef={inputRef}
            value={currentMessage}
            onChange={(e) => setCurrentMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything about your project..."
            multiline
            maxRows={3}
            fullWidth
            variant="outlined"
            disabled={isLoading}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                fontFamily: '"Recursive Variable", sans-serif'
              }
            }}
          />
          <Tooltip title="Send message">
            <IconButton
              onClick={() => handleSendMessage()}
              disabled={!currentMessage.trim() || isLoading}
              sx={{
                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',
                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',
                '&:hover': {
                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'
                }
              }}
            >
              <Iconify icon="material-symbols:send" width={20} height={20} />
            </IconButton>
          </Tooltip>
        </Box>
      </Paper>
    </Box>
  );
};

export default AgentTab;
