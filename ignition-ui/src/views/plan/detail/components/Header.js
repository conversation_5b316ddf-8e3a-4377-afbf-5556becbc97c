import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Tooltip,
  Chip,
  ToggleButtonGroup,
  ToggleButton
} from '@mui/material';
import { useSelector } from 'react-redux';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from '../styles.module.scss';

// Simple date formatter function
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
};

const Header = ({
  planInfo,
  viewMode,
  onViewModeChange,
  onOpenInviteDialog,
  onOpenDeleteDialog,
  onOpenOptOutDialog,
  onOpenEditDialog
}) => {
  const currentUser = useSelector((state) => state.user);

  const handleViewModeChange = (event, newMode) => {
    if (newMode !== null) {
      onViewModeChange(newMode);
    }
  };

  // Check if current user is the plan owner
  const isOwner = planInfo?.user?.id === currentUser?.id;

  return (
    <Box className={styles.header}>
      <Box className={styles.titleSection}>
        <Typography 
          variant="h4" 
          className={styles.pageTitle}
          sx={{ 
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 700,
            fontSize: { xs: '1.5rem', md: '1.8rem' }
          }}
        >
          {planInfo?.name}
        </Typography>
        
        {planInfo?.is_collaborative && (
          <Tooltip title="You are collaborating on this plan">
            <Chip
              icon={<Iconify icon="material-symbols:group" width={16} height={16} />}
              label="Collaborating"
              size="small"
              sx={{ 
                backgroundColor: `${mainYellowColor}20`, 
                color: mainYellowColor,
                fontWeight: 600,
                height: '24px',
                borderRadius: '4px',
                fontFamily: '"Recursive Variable", sans-serif',
                '& .MuiChip-label': {
                  px: 1
                }
              }}
            />
          </Tooltip>
        )}
        
        {planInfo?.created_at && (
          <Tooltip title={`Created on ${formatDate(planInfo.created_at)}`}>
            <Typography 
              variant="body2" 
              sx={{ 
                color: '#666', 
                display: 'flex', 
                alignItems: 'center',
                gap: 0.5,
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              <Iconify icon="material-symbols:calendar-today" width={14} height={14} />
              {formatDate(planInfo.created_at)}
            </Typography>
          </Tooltip>
        )}
      </Box>
      
      <Box className={styles.actionButtons}>
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewModeChange}
          size="small"
          sx={{ 
            mr: 1,
            '& .MuiToggleButton-root': {
              border: '1px solid #e0e0e0',
              borderRadius: '4px',
              mx: 0.5,
              p: 0.5,
              color: '#666',
              '&.Mui-selected': {
                backgroundColor: `${mainYellowColor}20`,
                color: mainYellowColor,
                borderColor: mainYellowColor
              }
            }
          }}
        >
          <ToggleButton value="list">
            <Iconify icon="material-symbols:view-list" width={20} height={20} />
          </ToggleButton>
          <ToggleButton value="grid">
            <Iconify icon="material-symbols:grid-view" width={20} height={20} />
          </ToggleButton>
        </ToggleButtonGroup>
        
        <Box className={styles.planActions}>
          <Tooltip title="Invite members">
            <IconButton 
              onClick={onOpenInviteDialog}
              sx={{ 
                color: '#666',
                border: '1px solid #e0e0e0',
                borderRadius: '4px',
                p: 0.5
              }}
            >
              <Iconify icon="material-symbols:person-add" width={20} height={20} />
            </IconButton>
          </Tooltip>
          
          {isOwner && (
            <Tooltip title="Edit plan">
              <IconButton
                onClick={onOpenEditDialog}
                sx={{
                  color: '#666',
                  border: '1px solid #e0e0e0',
                  borderRadius: '4px',
                  p: 0.5
                }}
              >
                <Iconify icon="material-symbols:edit" width={20} height={20} />
              </IconButton>
            </Tooltip>
          )}

          {isOwner ? (
            <Tooltip title="Delete plan">
              <IconButton
                onClick={onOpenDeleteDialog}
                sx={{
                  color: '#f44336',
                  border: '1px solid #ffcdd2',
                  borderRadius: '4px',
                  p: 0.5
                }}
              >
                <Iconify icon="material-symbols:delete" width={20} height={20} />
              </IconButton>
            </Tooltip>
          ) : (
            <Tooltip title="Opt Out">
              <IconButton
                onClick={onOpenOptOutDialog}
                sx={{
                  color: '#f44336',
                  border: '1px solid #ffcdd2',
                  borderRadius: '4px',
                  p: 0.5
                }}
              >
                <Iconify icon="material-symbols:logout" width={20} height={20} />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Header; 