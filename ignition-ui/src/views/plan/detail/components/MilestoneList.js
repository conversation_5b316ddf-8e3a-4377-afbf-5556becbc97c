import React from 'react';
import { Box, Typography } from '@mui/material';
import MilestoneCard from './MilestoneCard';
import styles from '../styles.module.scss';

const MilestoneList = ({ 
  milestones, 
  viewMode, 
  compact = false, 
  showSubtasks = false,
  calculateMilestoneProgress,
  getMilestoneStatus,
  calculateTaskProgress,
  getTaskStatus,
  calculateSubtaskProgress,
  getSubtaskStatus,
  onUpdateMilestone,
  onUpdateTask,
  onUpdateSubtask,
  onAddTask,
  onAddSubtask,
  onDeleteTask,
  onDeleteSubtask,
  onAssignMembers,
  invitedUsers,
  planOwner
}) => {
  if (!milestones || milestones.length === 0) {
    return (
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body1" sx={{ color: '#666' }}>
          No milestones found for this plan.
        </Typography>
      </Box>
    );
  }

  return (
    <Box className={styles.milestoneList}>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: 2, 
          fontFamily: '"Recursive Variable", sans-serif',
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}
      >
        Milestones ({milestones.length})
      </Typography>
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {milestones.map((milestone, index) => (
          <MilestoneCard 
            key={index} 
            milestone={milestone} 
            showSubtasks={showSubtasks}
            compact={compact}
            calculateMilestoneProgress={calculateMilestoneProgress}
            getMilestoneStatus={getMilestoneStatus}
            calculateTaskProgress={calculateTaskProgress}
            getTaskStatus={getTaskStatus}
            calculateSubtaskProgress={calculateSubtaskProgress}
            getSubtaskStatus={getSubtaskStatus}
            onUpdateMilestone={onUpdateMilestone}
            onUpdateTask={onUpdateTask}
            onUpdateSubtask={onUpdateSubtask}
            onAddTask={onAddTask}
            onAddSubtask={onAddSubtask}
            onDeleteTask={onDeleteTask}
            onDeleteSubtask={onDeleteSubtask}
            onAssignMembers={onAssignMembers}
            invitedUsers={invitedUsers}
            planOwner={planOwner}
          />
        ))}
      </Box>
    </Box>
  );
};

export default MilestoneList; 