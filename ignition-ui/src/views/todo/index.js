/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { Box, Typography, IconButton, Fab, Skeleton, Paper, Tooltip } from '@mui/material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { getHeaders } from "helpers/functions";
import { APIURL, mainYellowColor } from "helpers/constants";
import {
  TASK_SECTIONS, TASK_COMPLETED, TODO_STATUS, DONE_STATUS, dateFormatter, dateFormatterWithName
} from './components/constants';
import DateSelectionDialog from './components/dateSelectionDialog';
import ConfirmDialogComponent from 'components/Dialog/confirm';
import AddTaskDialog from './components/addTaskDialog';
import Iconify from 'components/Iconify/index';
import styles from './styles.module.scss';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

const TaskList = () => {
  const [tasks, setTasks] = useState([]);
  const [openAddTaskModal, setOpenAddTaskModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newTask, setNewTask] = useState({
    start_date: dayjs(),
    end_date: dayjs(),
    plan_name: "",
    subtasks: []
  });
  const [selectedTask, setSelectedTask] = useState(null);
  const [dateSelectionModal, setDateSelectionModal] = useState(false);
  // const [destinationSection, setDestinationSection] = useState(null);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [deletingTaskId, setDeletingTaskId] = useState(null);

  // Set CSS variable for main yellow color
  useEffect(() => {
    document.documentElement.style.setProperty('--main-yellow-color', mainYellowColor);
  }, []);

  const fetchTasks = async () => {
    setLoading(true);
    await apiCallWithErrorHandling(
      async () => {
        const response = await axios.get(`${APIURL}/api/tasks/todo-by-status`, { headers: getHeaders() });
        setTasks(response.data);
      },
      'Error loading tasks'
    );
    setLoading(false);
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  const handleAddTask = async () => {
    const task = {
      start_date: newTask.start_date.format(dateFormatter),
      end_date: newTask.end_date.format(dateFormatter),
      plan_name: newTask.plan_name,
      subtasks: newTask.subtasks,
      status: TODO_STATUS,
    };

    await apiCallWithErrorHandling(
      async () => {
        await axios.post(`${APIURL}/api/task/add`, task, { headers: getHeaders() });
        setOpenAddTaskModal(false);
        fetchTasks();
      },
      'Error adding task'
    );
  };

  const handleDeleteClick = (taskId) => {
    setDeletingTaskId(taskId);
    setConfirmDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deletingTaskId) {
      try {
        await axios.delete(`${APIURL}/api/task/delete/${deletingTaskId}`, { headers: getHeaders() });
        fetchTasks();
      } catch (error) {
        console.log('Error deleting task: ', error);
      } finally {
        setConfirmDialogOpen(false);
        setDeletingTaskId(null);
      }
    }
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
    setDeletingTaskId(null);
  };

  const updateTaskSection = async (task, newStatus, startDate = null, endDate = null) => {
    await apiCallWithErrorHandling(
      async () => {
        const formData = new FormData();
        formData.append('status', newStatus);
        if (startDate) formData.append('start_date', startDate);
        if (endDate) formData.append('end_date', endDate);

        await axios.post(`${APIURL}/api/tasks/${task.slug}/update-todo-status`, formData, { headers: getHeaders() });
        fetchTasks();
      },
      'Error updating task section'
    );
  };

  const onDragEnd = (result) => {
    const { source, destination } = result;

    if (!destination) return;
    if (source.droppableId === destination.droppableId && source.index === destination.index) return;

    const draggedTask = tasks.find((task) => task.id === parseInt(result.draggableId));
    if (!draggedTask) return;

    const destSection = destination.droppableId;
    if (destSection === TASK_COMPLETED) {
      updateTaskSection(draggedTask, DONE_STATUS);
    } else {
      setSelectedTask(draggedTask);
      // setDestinationSection(destSection);
      setDateSelectionModal(true);
    }
  };

  const apiCallWithErrorHandling = async (callback, errorMessage) => {
    try {
      await callback();
    } catch (error) {
      console.error(errorMessage, error);
    }
  };

  const handleDateUpdate = (startDate, endDate) => {
    if (selectedTask) {
      const formattedStartDate = startDate ? startDate.format(dateFormatter) : null;
      const formattedEndDate = endDate ? endDate.format(dateFormatter) : null;
      updateStartEndDate(selectedTask, formattedStartDate, formattedEndDate);
      setSelectedTask(null);
      setDateSelectionModal(false);
    }
  };

  const updateStartEndDate = async (task, startDate, endDate) => {
    await apiCallWithErrorHandling(
      async () => {
        const formData = new FormData();
        if (startDate) formData.append('start_date', startDate);
        if (endDate) formData.append('end_date', endDate);

        await axios.post(`${APIURL}/api/tasks/${task.slug}/update-start-end-date`, formData, { headers: getHeaders() });
        fetchTasks();
      },
      'Error updating start and end dates'
    );
  };

  const categorizeTasks = (tasks) => {
    const today = dayjs();

    return {
      completed: tasks.filter(task => task.status === DONE_STATUS),
      current: tasks.filter(task =>
        task.status !== DONE_STATUS &&
        dayjs(task.start_date).isSameOrBefore(today, 'day') &&
        (!task.end_date || dayjs(task.end_date).isSameOrAfter(today, 'day'))
      ),
      upcoming: tasks.filter(task =>
        task.status !== DONE_STATUS &&
        dayjs(task.start_date).isAfter(today, 'day')
      ),
      overdue: tasks.filter(task =>
        task.status !== DONE_STATUS &&
        task.end_date &&
        dayjs(task.end_date).isBefore(today, 'day')
      ),
    };
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Box className={styles.taskListContainer}>
        <Typography
          variant="h5"
          className={styles.pageTitle}
          sx={{ fontFamily: 'Recursive Variable, sans-serif' }}
        >
          <Iconify icon="material-symbols:checklist" width={28} height={28} color={mainYellowColor} />
          Task Management
        </Typography>

        <Paper
          elevation={0}
          className={styles.pageContainer}
          sx={{
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            backgroundColor: '#fff',
            p: { xs: 1.5, sm: 2 },
            display: 'flex',
            flexDirection: 'column',
            flex: 1,
            overflow: 'hidden'
          }}
        >
          <Box className={styles.columnsContainer}>
            {TASK_SECTIONS.map((section) => (
              <Box
                key={section.id}
                className={styles.taskBox}
                data-section={section.id.toLowerCase()}
              >
                <Typography
                  variant="h6"
                  className={styles.header}
                  sx={{ fontFamily: 'Recursive Variable, sans-serif' }}
                  data-section={section.id.toLowerCase()}
                >
                  {section.title}
                </Typography>
                <Droppable droppableId={section.id}>
                  {(provided) => (
                    <Box
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={styles.section}
                    >
                      {loading ? (
                        [...Array(2)].map((_, index) => (
                          <Paper key={index} className={styles.taskItem} elevation={0}>
                            <Skeleton variant="text" width="80%" height={24} />
                            <Skeleton variant="text" width="60%" height={20} />
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                              <Skeleton variant="text" width="40%" height={20} />
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <Skeleton variant="circular" width={32} height={32} />
                                <Skeleton variant="circular" width={32} height={32} />
                                <Skeleton variant="circular" width={32} height={32} />
                              </Box>
                            </Box>
                          </Paper>
                        ))
                      ) : (
                        <>
                          {categorizeTasks(tasks)[section.id.toLowerCase()]?.length === 0 ? (
                            <Typography
                              className={styles.noContent}
                              sx={{ fontFamily: 'Recursive Variable, sans-serif' }}
                            >
                              No tasks in this section
                            </Typography>
                          ) : (
                            categorizeTasks(tasks)[section.id.toLowerCase()]?.map((task, index) => (
                              <Draggable key={task.id} draggableId={String(task.id)} index={index}>
                                {(provided) => (
                                  <Box
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    className={styles.taskItem}
                                  >
                                    <Typography
                                      className={styles.taskTitle}
                                      sx={{ fontFamily: 'Recursive Variable, sans-serif' }}
                                    >
                                      {task.name}
                                    </Typography>
                                    <Typography
                                      className={styles.planName}
                                      sx={{ fontFamily: 'Recursive Variable, sans-serif' }}
                                    >
                                      {task.plan_name || "General"}
                                    </Typography>

                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
                                      <Box className={styles.taskDateCover}>
                                        <Iconify icon="material-symbols:schedule" width={16} height={16} color="#64748b" />
                                        <Typography
                                          className={styles.taskDate}
                                          sx={{ fontFamily: 'Recursive Variable, sans-serif' }}
                                        >
                                          {task.start_date && task.end_date ? (
                                            `${dayjs(task.start_date).format(dateFormatterWithName)} ~ ${dayjs(task.end_date).format(dateFormatterWithName)}`
                                          ) : (
                                            'Not set'
                                          )}
                                        </Typography>
                                      </Box>

                                      <Box className={styles.taskActions}>
                                        <Tooltip title="Edit Dates">
                                          <IconButton
                                            className={styles.taskIcon}
                                            onClick={() => { setSelectedTask(task); setDateSelectionModal(true); }}
                                            size="small"
                                          >
                                            <Iconify icon="material-symbols:calendar-edit" width={18} height={18} />
                                          </IconButton>
                                        </Tooltip>
                                        <Tooltip title={task.status === DONE_STATUS ? "Mark as Incomplete" : "Mark as Complete"}>
                                          <IconButton
                                            className={styles.taskCheckbox}
                                            onClick={() => updateTaskSection(task, task.status === DONE_STATUS ? TODO_STATUS : DONE_STATUS)}
                                            size="small"
                                          >
                                            <Iconify
                                              icon={task.status === DONE_STATUS ? "material-symbols:check-circle" : "material-symbols:radio-button-unchecked"}
                                              width={18}
                                              height={18}
                                              sx={{ color: task.status === DONE_STATUS ? mainYellowColor : undefined }}
                                            />
                                          </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Delete Task">
                                          <IconButton
                                            className={styles.taskDelete}
                                            onClick={() => handleDeleteClick(task.slug)}
                                            size="small"
                                          >
                                            <Iconify icon="material-symbols:delete-outline" width={18} height={18} />
                                          </IconButton>
                                        </Tooltip>
                                      </Box>
                                    </Box>
                                  </Box>
                                )}
                              </Draggable>
                            ))
                          )}
                          {provided.placeholder}
                        </>
                      )}
                    </Box>
                  )}
                </Droppable>
              </Box>
            ))}
          </Box>
        </Paper>

        <Fab
          aria-label="add"
          className={styles.addBtn}
          onClick={() => setOpenAddTaskModal(true)}
          size="medium"
          sx={{ backgroundColor: `${mainYellowColor} !important`, color: 'white !important' }}
        >
          <Iconify icon="material-symbols:add" width={24} height={24} sx={{ color: 'white !important' }} />
        </Fab>

        <AddTaskDialog
          open={openAddTaskModal}
          handleClose={() => setOpenAddTaskModal(false)}
          newTask={newTask}
          setNewTask={setNewTask}
          handleAddTask={handleAddTask}
        />

        <DateSelectionDialog
          open={dateSelectionModal}
          handleClose={() => setDateSelectionModal(false)}
          task={selectedTask}
          handleDateUpdate={handleDateUpdate}
        />

        <ConfirmDialogComponent
          open={confirmDialogOpen}
          onClose={handleCloseConfirmDialog}
          onConfirm={handleConfirmDelete}
          title="Confirm Delete"
          description="Are you sure you want to delete this task?"
        />
      </Box>
    </DragDropContext>
  );
};

export default TaskList;
